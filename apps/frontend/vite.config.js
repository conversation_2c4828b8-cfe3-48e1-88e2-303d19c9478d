import Vue from '@vitejs/plugin-vue'
import Autoprefixer from 'autoprefixer'
import Tailwind from 'tailwindcss'
import { defineConfig } from 'vite'
import { fileURLToPath, URL } from 'node:url'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import UnpluginUnused from 'unplugin-unused/vite'
import VueRouter from 'unplugin-vue-router/vite'

// https://vite.dev/config/
export default defineConfig({
  root: process.cwd(),
  port: 5173,
  server: {
    fs: {
      // Explicitly restrict filesystem access to the client directory
      allow: [process.cwd()],
    },
  },
  css: {
    postcss: {
      plugins: [Tailwind(), Autoprefixer()],
    },
  },
  plugins: [
    Vue(),
    VueRouter(),
    Components({
      dirs: ['src/components/**', 'src/shadcn/ui/**/', 'src/components/ui/**'],
      dts: true,
      directoryAsNamespace: true,
    }),
    UnpluginUnused(),
    AutoImport({
      include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
      // see: https://github.com/unovue/shadcn-vue/issues/172
      exclude: [
        'src/shadcn/ui/form/**',
        'src/shadcn/ui/form/index.ts',
        'src/shadcn/ui/form/injectionKeys.ts',
        'src/shadcn/ui/form/useFormField.ts',
      ],
      imports: [
        'vue',
        'pinia',
        {
          '@vueuse/integrations/useCookies': ['useCookies'],
        },
        {
          '@/stores': ['useAppStore', 'useDialogStore', 'useThemeStore'],
        },
        {
          '@/common/constants': ['useConstants'],
        },
        {
          '@/shadcn/ui/toast/use-toast': ['useToast'],
        },
      ],
      dirs: [
        'src/constants.ts',
        'src/components/**',
        'src/shadcn/ui/**/index.ts',
        'src/shadcn/ui/**/*.vue',
        'src/composables/**',
        'src/components/ui/toast/**',
        'src/stores/**',
        'src/plugins/**',
      ],
      viteOptimizeDeps: true,
      dts: './auto-imports.d.ts',
      eslintrc: {
        enabled: false,
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true,
      },
      resolvers: [
        (name) => {
          if (name.startsWith('Shadcn')) {
            return { from: `src/shadcn/ui/${name.slice(6).toLowerCase()}`, name }
          }
        },
      ],
    }),
  ],
  optimizeDeps: {
    exclude: ['@insertchat/server'],
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'src': fileURLToPath(new URL('./src', import.meta.url)),
      '@src': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
