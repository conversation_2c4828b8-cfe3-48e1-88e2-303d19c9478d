<template>
  <Toaster />
  <DialogProvider />
  <template v-if="appStore.isNavigating">
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-white/80">
      <BaseSkeleton type="app" :rows="0" :headers="0" :cols="0" />
    </div>
  </template>
  <Suspense v-else>
    <template #default>
      <router-view />
    </template>
    <template #fallback>
      <div class="fixed inset-0 z-50 flex items-center justify-center bg-white/80">
        <BaseSkeleton type="app" :rows="0" :headers="0" :cols="0" />
      </div>
    </template>
  </Suspense>
  <StagewiseToolbar v-if="isDevMode" :config="stagewiseConfig" />
</template>

<script setup lang="ts">
import { StagewiseToolbar } from '@stagewise/toolbar-vue'

const themeStore = useThemeStore()
const appStore = useAppStore()

themeStore.setColorMode('light')

const isDevMode = import.meta.env.DEV

const stagewiseConfig = {
  plugins: [],
}
</script>
