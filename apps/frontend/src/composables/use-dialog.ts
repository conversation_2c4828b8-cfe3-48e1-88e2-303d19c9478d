import { ref, computed } from 'vue'
import { useDialogStore, type DialogType, type DialogProps } from '@/stores/dialog'

export interface UseDialogOptions {
  id?: string
  autoClose?: boolean
}

export function useDialog(options: UseDialogOptions = {}) {
  const dialogStore = useDialogStore()
  const {
    id = `dialog-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    autoClose = true,
  } = options

  // Local state for simple dialogs
  const isOpen = ref(false)

  // Computed to check if dialog instance is open
  const isInstanceOpen = computed(() => dialogStore.isDialogOpen(id))

  // Open dialog using local state
  function open() {
    isOpen.value = true
  }

  // Close dialog using local state
  function close() {
    isOpen.value = false
  }

  // Open dialog using store instance
  function openInstance(type: DialogType, props?: DialogProps) {
    dialogStore.openDialog(id, type, props)
  }

  // Close dialog instance
  function closeInstance() {
    dialogStore.closeDialog(id)
  }

  // Toggle local dialog
  function toggle() {
    isOpen.value = !isOpen.value
  }

  // Toggle dialog instance
  function toggleInstance(type?: DialogType, props?: DialogProps) {
    if (isInstanceOpen.value) {
      closeInstance()
    } else if (type) {
      openInstance(type, props)
    }
  }

  return {
    // Dialog ID
    id,

    // Local state management
    isOpen,
    open,
    close,
    toggle,

    // Store instance management
    isInstanceOpen,
    openInstance,
    closeInstance,
    toggleInstance,
  }
}

// Utility function to create a dialog with a specific type
export function useTypedDialog(type: DialogType, options: UseDialogOptions = {}) {
  const dialog = useDialog(options)

  function openWithProps(props?: DialogProps) {
    dialog.openInstance(type, props)
  }

  return {
    ...dialog,
    open: openWithProps,
    openWithProps,
  }
}
