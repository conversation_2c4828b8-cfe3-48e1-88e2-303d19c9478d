<script setup lang="ts">
import { useSlots, computed } from 'vue'
import { useDialogStore } from '@/stores/dialog'
import { storeToRefs } from 'pinia'
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogFooter } from '@/shadcn/ui/dialog'

const props = defineProps<{
  title: string
  description: string
  icon: string
  open: boolean
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

const slots = useSlots()
const dialogStore = useDialogStore()
const { state } = storeToRefs(dialogStore)
const { close } = dialogStore

// Use local open prop if provided, otherwise fall back to global store
const isOpen = computed(() => (props.open !== undefined ? props.open : state.value.isOpen))

function handleOpenChange(value: boolean) {
  if (props.open !== undefined) {
    // If using v-model:open, emit the event
    emit('update:open', value)
  } else {
    // Otherwise use global store
    if (!value) close()
  }
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="handleOpenChange">
    <DialogContent
      class="flex flex-col w-[100vw] h-[100vh] max-w-[100vw] min-w-[100vw] !rounded-none p-2"
    >
      <div v-if="title || description" class="flex-none border-b">
        <DialogHeader class="mb-2">
          <h2 class="flex items-center space-x-2 text-lg font-medium">
            <BaseIcon :name="icon" v-if="icon" />
            <span> {{ title }}</span>
          </h2>
          <p v-if="description" class="text-sm text-muted-foreground">
            {{ description }}
          </p>
        </DialogHeader>
      </div>

      <ScrollArea class="flex-1 w-full">
        <div class="w-full md:w-[100%] mx-auto px-2 h-full">
          <slot />
        </div>
      </ScrollArea>

      <div v-if="$slots.footer" class="flex-none border-t">
        <DialogFooter class="mt-2">
          <div class="flex items-end justify-end">
            <slot name="footer" />
          </div>
        </DialogFooter>
      </div>
    </DialogContent>
  </Dialog>
</template>
