<script setup lang="ts">
import { computed, watchEffect } from 'vue'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogFooter } from '@/shadcn/ui/dialog'

const props = defineProps<{
  title: string
  description: string
  icon: string
  open?: boolean
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

// Use the open prop directly since we removed global store fallback
const isOpen = computed(() => props.open || false)

// Debug: Log when BaseDialog is created and when open state changes
console.log('BaseDialog created with props:', props)
watchEffect(() => {
  console.log('BaseDialog isOpen changed:', isOpen.value, 'for dialog:', props.title)
})

function handleOpenChange(value: boolean) {
  if (props.open !== undefined) {
    // If using v-model:open, emit the event
    emit('update:open', value)
  } else {
    // Otherwise use global store
    if (!value) close()
  }
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="handleOpenChange">
    <DialogContent
      class="flex flex-col w-[100vw] h-[100vh] max-w-[100vw] min-w-[100vw] !rounded-none p-2"
    >
      <div v-if="title || description" class="flex-none border-b">
        <DialogHeader class="mb-2">
          <h2 class="flex items-center space-x-2 text-lg font-medium">
            <BaseIcon :name="icon" v-if="icon" />
            <span> {{ title }}</span>
          </h2>
          <p v-if="description" class="text-sm text-muted-foreground">
            {{ description }}
          </p>
        </DialogHeader>
      </div>

      <ScrollArea class="flex-1 w-full">
        <div class="w-full md:w-[100%] mx-auto px-2 h-full">
          <slot />
        </div>
      </ScrollArea>

      <div v-if="$slots.footer" class="flex-none border-t">
        <DialogFooter class="mt-2">
          <div class="flex items-end justify-end">
            <slot name="footer" />
          </div>
        </DialogFooter>
      </div>
    </DialogContent>
  </Dialog>
</template>
