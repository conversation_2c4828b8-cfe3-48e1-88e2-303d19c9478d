<script setup lang="ts">
import { computed } from 'vue'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, DialogFooter } from '@/shadcn/ui/dialog'
import { useDialogStore } from '@/stores/dialog'

const props = defineProps<{
  title: string
  description: string
  icon: string
  open?: boolean
  onCloseDialog?: () => void // Keep for backward compatibility
  dialogId?: string // New: direct dialog instance ID
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

const dialogStore = useDialogStore()

// Use the open prop directly since we removed global store fallback
const isOpen = computed(() => props.open || false)

function handleOpenChange(value: boolean) {
  if (props.open !== undefined) {
    // If using v-model:open, emit the event
    emit('update:open', value)
  }

  // If the dialog is being closed
  if (!value) {
    // Priority 1: Use direct dialog store if we have dialogId
    if (props.dialogId) {
      dialogStore.closeDialog(props.dialogId)
    }
    // Priority 2: Fallback to onCloseDialog prop for backward compatibility
    else if (props.onCloseDialog) {
      props.onCloseDialog()
    }
  }
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="handleOpenChange">
    <DialogContent
      class="flex flex-col w-[100vw] h-[100vh] max-w-[100vw] min-w-[100vw] !rounded-none p-2"
    >
      <div v-if="title || description" class="flex-none border-b">
        <DialogHeader class="mb-2">
          <h2 class="flex items-center space-x-2 text-lg font-medium">
            <BaseIcon :name="icon" v-if="icon" />
            <span> {{ title }}</span>
          </h2>
          <p v-if="description" class="text-sm text-muted-foreground">
            {{ description }}
          </p>
        </DialogHeader>
      </div>

      <ScrollArea class="flex-1 w-full">
        <div class="w-full md:w-[100%] mx-auto px-2 h-full">
          <slot />
        </div>
      </ScrollArea>

      <div v-if="$slots.footer" class="flex-none border-t">
        <DialogFooter class="mt-2">
          <div class="flex items-end justify-end">
            <slot name="footer" />
          </div>
        </DialogFooter>
      </div>
    </DialogContent>
  </Dialog>
</template>
