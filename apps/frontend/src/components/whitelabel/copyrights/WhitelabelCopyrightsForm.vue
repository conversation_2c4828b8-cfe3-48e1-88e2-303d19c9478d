<template>
  <BaseDialog
    :open="isDialogOpen"
    @update:open="isDialogOpen = $event"
    :icon="ICONS.COPYRIGHT"
    :title="title"
    :description="description"
  >
    <BaseForm :isLoading="isLoading" :isSubmitting="isSubmitting" :inputs="3" @submit="onSubmit">
      <FormField v-slot="{ componentField }" name="agent_uid">
        <FormItem>
          <FormLabel>Agent</FormLabel>
          <FormControl>
            <Select v-bind="componentField">
              <SelectTrigger>
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="agent in agents" :key="agent.uid" :value="agent.uid">
                  {{ agent.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="text">
        <FormItem>
          <FormLabel>Copyright Text</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="© 2024 Your Company" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="url">
        <FormItem>
          <FormLabel>URL</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="https://example.com" type="url" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <DialogFooter>
        <Button type="button" variant="outline" @click="closeDialog"> Cancel </Button>
        <Button type="submit" :disabled="isSubmitting">
          {{ editingUid ? 'Update' : 'Add' }}
        </Button>
      </DialogFooter>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { queryCopyrightById, mutateCopyright } from '@/services/whitelabel_service'
import { queryAgents } from '@/services/agent_service'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shadcn/ui/form'
import { ICONS } from '@/constants'
import type { FormComponent } from '@/types'

// Composables
const { toast } = useToast()
const { handleSubmit, isSubmitting, setValues } = useForm({
  initialValues: {
    uid: undefined as string | undefined,
    agent_uid: '',
    text: '',
    url: '',
  },
  validationSchema: toTypedSchema(
    z.object({
      uid: z.string().optional(),
      agent_uid: z.string().min(1, { message: 'Agent is required' }),
      text: z.string().min(1, { message: 'Copyright text is required' }),
      url: z.string().url({ message: 'Valid URL is required' }),
    })
  ),
})

// Refs
const isDialogOpen = ref(false)
const editingUid = ref<string | undefined>()

// Queries
const mutation = mutateCopyright(editingUid)
const { isLoading, isError, data } = queryCopyrightById(editingUid)
const { data: agentsData } = queryAgents()

// Computed
const title = computed(() => `${editingUid.value ? 'Edit' : 'Add'} Copyright`)
const description = computed(
  () => `${editingUid.value ? 'Edit a copyright entry' : 'Add a new copyright entry'}`
)
const agents = computed(() => agentsData.value?.data ?? [])

// Functions
const openDialog = (uid?: string) => {
  editingUid.value = uid
  isDialogOpen.value = true
}

const closeDialog = () => {
  isDialogOpen.value = false
}

const onSubmit = handleSubmit(async (values) => {
  try {
    const payload = {
      agent_uid: values.agent_uid,
      text: values.text,
      url: values.url,
    }

    if (editingUid.value) {
      await mutation.mutateAsync({ ...payload, uid: editingUid.value })
    } else {
      await mutation.mutateAsync(payload)
    }

    toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })

    closeDialog()
  } catch (err: any) {
    toast({
      title: `Failed to ${editingUid.value ? 'update' : 'add'}`,
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})

// Watchers
watchEffect(() => {
  if (!editingUid.value) return

  if (isLoading.value) return

  if (isError.value) {
    toast({
      title: 'Error Loading Copyright',
      description: `Copyright with ID ${editingUid.value} not found.`,
      variant: 'destructive',
    })
    closeDialog()
    return
  }

  if (data.value) {
    setValues({
      uid: editingUid.value,
      agent_uid: data.value.agent_uid ?? '',
      text: data.value.text ?? '',
      url: data.value.url ?? '',
    })
  }
})

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
