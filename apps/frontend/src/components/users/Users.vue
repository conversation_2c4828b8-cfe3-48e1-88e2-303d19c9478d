<template>
  <BaseDialog
    :open="dialog.isOpen.value"
    @update:open="dialog.isOpen.value = $event"
    :icon="ICONS.USERS"
    title="Users"
    description="Manage your users."
  >
    <UsersTable :onEdit="handleEdit" :onDelete="handleDelete" />
  </BaseDialog>

  <UsersForm ref="formRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UsersForm from './UsersForm.vue'
import { ICONS } from '@/constants'
import { mutateUserDestroy } from '@/services/user_service'
import type { FormComponent } from '@/types'
import { useDialog } from '@/composables/use-dialog'

// Composables
const { toast } = useToast()
const dialog = useDialog({ id: 'users-main' })

// Refs
const formRef = ref<typeof UsersForm>()

// Functions
const openDialog = () => {
  dialog.open()
}

const closeDialog = () => {
  dialog.close()
}

const handleEdit = (uid?: string) => {
  formRef.value?.openDialog(uid)
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateUserDestroy(ref(uid))

  try {
    await mutateAsync(undefined)

    toast({
      title: 'User Deleted',
      description: 'The user has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting User',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
