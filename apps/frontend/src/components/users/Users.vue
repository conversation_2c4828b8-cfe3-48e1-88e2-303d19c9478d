<template>
  <BaseDialog
    :open="true"
    :icon="ICONS.USERS"
    title="Users"
    description="Manage your users."
    :onCloseDialog="onCloseDialog"
  >
    <UsersTable :onEdit="handleEdit" :onDelete="handleDelete" />
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ICONS } from '@/constants'
import { mutateUserDestroy } from '@/services/user_service'
import type { FormComponent } from '@/types'
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'

// Props that can be passed from the dialog store
defineProps<{
  onCloseDialog?: () => void
}>()

// Composables
const { toast } = useToast()

// Functions
const openDialog = () => {
  // This function is for external API compatibility
  // When called, it should open via the dialog store
  const dialogStore = useDialogStore()
  dialogStore.openDialog(DIALOG_KEYS.USERS)
}

const closeDialog = () => {
  // This function is for external API compatibility
  const dialogStore = useDialogStore()
  dialogStore.closeDialogByKey(DIALOG_KEYS.USERS)
}

const handleEdit = (uid?: string) => {
  console.log('handleEdit', uid)

  const dialogStore = useDialogStore()
  dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, {
    uid,
  })
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateUserDestroy(ref(uid))

  try {
    await mutateAsync(undefined)

    toast({
      title: 'User Deleted',
      description: 'The user has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting User',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
