<template>
  <BaseDialog :open="true" :icon="ICONS.USERS" title="Users" description="Manage your users.">
    <UsersTable :onEdit="handleEdit" :onDelete="handleDelete" />
  </BaseDialog>

  <UsersForm ref="formRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UsersForm from './UsersForm.vue'
import { ICONS } from '@/constants'
import { mutateUserDestroy } from '@/services/user_service'
import type { FormComponent } from '@/types'

// Composables
const { toast } = useToast()

// Refs
const formRef = ref<typeof UsersForm>()

// Functions
const openDialog = () => {
  // This function is for external API compatibility
  // When called, it should open via the dialog store
  const dialogStore = useDialogStore()
  dialogStore.openDialog('users-main', 'users')
}

const closeDialog = () => {
  // This function is for external API compatibility
  const dialogStore = useDialogStore()
  dialogStore.closeDialog('users-main')
}

const handleEdit = (uid?: string) => {
  formRef.value?.openDialog(uid)
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateUserDestroy(ref(uid))

  try {
    await mutateAsync(undefined)

    toast({
      title: 'User Deleted',
      description: 'The user has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting User',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
