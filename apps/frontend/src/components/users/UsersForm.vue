<template>
  <BaseDialog
    :open="true"
    :icon="ICONS.USERS"
    :title="title"
    :description="description"
    :dialogId="props.dialogId"
  >
    <BaseForm :isLoading="isLoading" :isSubmitting="isSubmitting" :inputs="5" @submit="onSubmit">
      <FormField v-slot="{ componentField }" name="first_name">
        <FormItem>
          <FormLabel>First Name</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="First Name" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="last_name">
        <FormItem>
          <FormLabel>Last Name</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="Last Name (optional)" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="email">
        <FormItem>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="<EMAIL>" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="role">
        <FormItem>
          <FormLabel>Role</FormLabel>
          <FormControl>
            <Select v-bind="componentField">
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="client">
                  Client
                  <span class="ml-2 text-xs text-muted-foreground">
                    - Access to assigned agents only.
                  </span>
                </SelectItem>
                <SelectItem value="manager">
                  Manager
                  <span class="ml-2 text-xs text-muted-foreground"> - Can manage agents. </span>
                </SelectItem>
                <SelectItem value="owner">
                  Owner
                  <span class="ml-2 text-xs text-muted-foreground">
                    - Full access to account, app, and subscriptions.
                  </span>
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-if="formValues.role === 'client'" name="assigned_agents">
        <FormItem>
          <FormLabel>Assigned Agents</FormLabel>

          <FormItem
            v-for="agent in agents"
            :key="agent.id"
            class="flex flex-row items-start space-x-3 space-y-0"
          >
            <FormControl>
              <Checkbox
                :id="agent.id"
                :value="agent.id"
                :checked="formValues.assigned_agents?.includes(agent.id)"
                @update:checked="
                  (isChecked) =>
                    isChecked
                      ? setFieldValue(
                          'assigned_agents',
                          [...(formValues.assigned_agents ?? []), agent.id],
                          true
                        )
                      : setFieldValue(
                          'assigned_agents',
                          (formValues.assigned_agents ?? []).filter((id) => id !== agent.id),
                          true
                        )
                "
              />
            </FormControl>

            <FormLabel class="font-normal">
              {{ agent.name }}
            </FormLabel>
          </FormItem>
        </FormItem>
      </FormField>

      <DialogFooter>
        <Button type="button" variant="outline" @click="closeDialog"> Cancel </Button>
        <Button type="submit" :disabled="isSubmitting">
          {{ editingUid ? 'Update' : 'Add' }}
        </Button>
      </DialogFooter>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed, onMounted } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { queryUserById, mutateUser } from '@/services/user_service'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shadcn/ui/form'
import { ICONS } from '@/constants'
import type { FormComponent } from '@/types'

// Props that can be passed from the dialog store
const props = defineProps<{
  uid?: string
  onCloseDialog?: () => void // Backward compatibility
  dialogId?: string // New: direct dialog instance ID
}>()

// Composables
const { toast } = useToast()
const {
  handleSubmit,
  isSubmitting,
  setValues,
  values: formValues,
  setFieldValue,
} = useForm({
  initialValues: {
    uid: undefined as string | undefined,
    first_name: '',
    last_name: '',
    email: '',
    role: 'client',
    assigned_agents: [],
  },
  validationSchema: toTypedSchema(
    z.object({
      uid: z.string().optional(),
      first_name: z.string().min(2, { message: 'First name is required' }),
      last_name: z.string().optional(),
      email: z.string().email({ message: 'Invalid email address' }),
      role: z.enum(['owner', 'manager', 'client']),
      assigned_agents: z.array(z.string()),
    })
  ),
})

// Refs
const editingUid = ref<string | undefined>()

// Queries
const mutation = mutateUser(editingUid)
const { isLoading, isError, data } = queryUserById(editingUid)

// Computed
const title = computed(() => `${editingUid.value ? 'Edit' : 'Add'} User`)
const description = computed(() => `${editingUid.value ? 'Edit a user' : 'Add a new user'}`)

const agents = ref<Array<{ id: string; name: string }>>([])

async function fetchAgents() {
  try {
    // agents.value = response as unknown as Array<{ id: string; name: string }>
  } catch (error) {
    toast({
      title: 'Error fetching agents',
      description: (error as Error)?.message || 'Could not load agents list.',
      variant: 'destructive',
    })
    agents.value = [] // Default to empty list on error
  }
}

onMounted(() => {
  fetchAgents()

  // Set the editing UID from props when component mounts
  if (props.uid) {
    editingUid.value = props.uid
  }
})

// Functions
const openDialog = (uid?: string) => {
  editingUid.value = uid
  // Dialog is managed by the centralized store, no need to open here
}

const closeDialog = () => {
  props.onCloseDialog?.()
}

const onSubmit = handleSubmit(async (values) => {
  try {
    const payload = {
      first_name: values.first_name,
      last_name: values.last_name,
      email: values.email,
      role: values.role,
      assigned_agents: values.assigned_agents || [],
    }

    if (editingUid.value) {
      await mutation.mutateAsync({ ...payload, uid: editingUid.value })
    } else {
      await mutation.mutateAsync(payload)
    }

    toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })

    closeDialog()
  } catch (err: any) {
    toast({
      title: `Failed to ${editingUid.value ? 'update' : 'add'}`,
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})

// Watchers
watchEffect(() => {
  if (!editingUid.value) return

  if (isLoading.value) return

  if (isError.value) {
    toast({
      title: 'Error Loading User',
      description: `User with ID ${editingUid.value} not found.`,
      variant: 'destructive',
    })
    closeDialog()
    return
  }

  if (data.value) {
    setValues({
      uid: editingUid.value,
      first_name: data.value.first_name ?? '',
      last_name: data.value.last_name ?? '',
      email: data.value.email ?? '',
      role: ['owner', 'manager', 'client'].includes(data.value.role as string)
        ? (data.value.role as 'owner' | 'manager' | 'client')
        : 'client',
      assigned_agents: data.value.assigned_agents ?? [],
    })
  }
})

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
