<template>
  <BaseDialog
    :open="true"
    title="Integrations"
    description="Manage your agent's integrations and connections"
    icon="Plug"
  >
    <BaseTabs
      default-value="js-events"
      :tabs="[
        {
          value: 'js-events',
          label: 'JS Events',
          icon: 'Code',
        },
        {
          value: 'webhooks',
          label: 'Webhooks',
          icon: 'Webhook',
        },
        {
          value: 'api',
          label: 'API',
          icon: 'FileText',
        },
        {
          value: 'zapier',
          label: 'Zapier',
          icon: 'Zap',
        },
        {
          value: 'whatsapp',
          label: 'WhatsApp',
          icon: 'MessageSquare',
        },
      ]"
    >
      <TabsContent value="js-events" class="w-full mt-0 overflow-y-auto">
        <AgentDialogIntegrationJsEvents :agentUid="agentUid" />
      </TabsContent>
      <TabsContent value="webhooks" class="w-full mt-0 overflow-y-auto">
        <AgentDialogIntegrationWebhooks :agentUid="agentUid" />
      </TabsContent>
      <TabsContent value="api" class="w-full mt-0 overflow-y-auto">
        <AgentDialogIntegrationApi :agentUid="agentUid" />
      </TabsContent>
      <TabsContent value="zapier" class="w-full mt-0 overflow-y-auto">
        <AgentDialogIntegrationZapier :agentUid="agentUid" />
      </TabsContent>
      <TabsContent value="whatsapp" class="w-full mt-0 overflow-y-auto">
        <AgentDialogIntegrationWhatsapp :agentUid="agentUid" />
      </TabsContent>
    </BaseTabs>

    <template #footer>
      <Button variant="outline" @click="handleClose">Close</Button>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
const AgentDialogIntegrationJsEvents = defineAsyncComponent(
  () => import('./agent-integration-events.vue')
)
const AgentDialogIntegrationWebhooks = defineAsyncComponent(
  () => import('./agent-integration-webhooks.vue')
)
const AgentDialogIntegrationApi = defineAsyncComponent(() => import('./agent-integration-api.vue'))
const AgentDialogIntegrationZapier = defineAsyncComponent(
  () => import('./agent-integration-zapier.vue')
)
const AgentDialogIntegrationWhatsapp = defineAsyncComponent(
  () => import('./agent-integration-whatsapp.vue')
)

const props = defineProps<{
  agentUid: string
  onClose: () => void
}>()

const handleClose = () => {
  props.onClose()
}
</script>

<style scoped>
@media (max-width: 640px) {
  :deep(.tabs-list) {
    overflow-x: auto;
    flex-wrap: nowrap;
    padding-bottom: 0.5rem;
  }

  :deep(.tabs-trigger) {
    flex-shrink: 0;
    white-space: nowrap;
  }
}
</style>
