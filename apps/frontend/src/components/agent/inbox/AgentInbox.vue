<template>
  <TooltipProvider>
    <BaseDialog
      :open="true"
      title="Inbox"
      description="Manage your conversations and leads."
      icon="Inbox"
    >
      <BaseTabs
        default-value="chats"
        :tabs="[
          { value: 'chats', label: 'Chats', icon: 'MessageSquare' },
          { value: 'leads', label: 'Leads', icon: 'Users' },
          { value: 'feedback', label: 'Feedback', icon: 'MessageCircle' },
        ]"
      >
        <TabsContent value="chats">
          <AgentChats :agentUid="props.agentUid" />
        </TabsContent>

        <TabsContent value="leads">
          <AgentLeads :agentUid="props.agentUid" />
        </TabsContent>

        <TabsContent value="feedback">
          <AgentFeedbacks :agentUid="props.agentUid" />
        </TabsContent>
      </BaseTabs>
    </BaseDialog>
  </TooltipProvider>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()
</script>
