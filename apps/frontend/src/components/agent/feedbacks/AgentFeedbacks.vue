<template>
  <BaseCard
    :open="true"
    title="Feedback"
    description="Manage feedback and create Q&A entries from them."
  >
    <div class="space-y-4">
      <!-- Filters -->
      <div class="flex flex-wrap items-center gap-2">
        <div class="flex-1 min-w-[200px]">
          <Input v-model="searchQuery" placeholder="Search feedback..." />
        </div>
        <Select v-model="statusFilter">
          <SelectTrigger class="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="positive">Positive</SelectItem>
            <SelectItem value="negative">Negative</SelectItem>
          </SelectContent>
        </Select>
        <Select v-model="qaFilter">
          <SelectTrigger class="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by Q&A" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Q&A Status</SelectItem>
            <SelectItem value="created">Q&A Created</SelectItem>
            <SelectItem value="pending">Q&A Pending</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- Feedback Table -->
      <AgentFeedbacksTable
        :feedbacks="filteredFeedback"
        @view-details="handleViewDetails"
        @create-qa="handleCreateQA"
        @delete-feedback="handleDeleteFeedback"
      />
    </div>
  </BaseCard>

  <!-- Feedback Details Dialog -->
  <BaseDialog :open="isDetailsOpen" @update:open="isDetailsOpen = $event">
    <DialogContent class="sm:max-w-[600px]">
      <DialogHeader>
        <DialogTitle>Feedback Details</DialogTitle>
        <DialogDescription>Detailed view of the selected feedback.</DialogDescription>
      </DialogHeader>
      <div v-if="selectedFeedback" class="space-y-4">
        <div class="space-y-2">
          <Label>Date</Label>
          <div class="text-sm text-muted-foreground">
            {{ formatDate(selectedFeedback.createdAt) }}
          </div>
        </div>
        <div class="space-y-2">
          <Label>Status</Label>
          <div>
            <Badge :variant="selectedFeedback.status === 'positive' ? 'outline' : 'destructive'">
              {{ selectedFeedback.status }}
            </Badge>
          </div>
        </div>
        <div class="space-y-2">
          <Label>Message</Label>
          <div class="p-3 text-sm border rounded-md bg-muted">
            {{ selectedFeedback.message }}
          </div>
        </div>
        <div class="space-y-2">
          <Label>Context</Label>
          <div class="p-3 border rounded-md bg-muted">
            <pre class="text-xs whitespace-pre-wrap">{{
              JSON.stringify(selectedFeedback.context, null, 2)
            }}</pre>
          </div>
        </div>
        <div class="space-y-2">
          <Label>Q&A Status</Label>
          <div>
            <Badge :variant="selectedFeedback.qaStatus === 'created' ? 'outline' : 'secondary'">
              {{ selectedFeedback.qaStatus === 'created' ? 'Q&A Created' : 'Pending' }}
            </Badge>
          </div>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="isDetailsOpen = false">Close</Button>
      </DialogFooter>
    </DialogContent>
  </BaseDialog>

  <!-- Create Q&A Form Dialog -->
  <AgentFeedbacksForm v-model:open="isQaFormOpen" :initialFormData="qaForm" @save="handleSaveQA" />
</template>

<script setup lang="ts">
import { useConfirmDialog } from '@/composables/use-confirm-dialog'
import { type Feedback, type QAForm } from '@/components/agent/feedbacks/types'

// State
const searchQuery = ref('')
const statusFilter = ref('all')
const qaFilter = ref('all')
const isDetailsOpen = ref(false)
const isQaFormOpen = ref(false)
const selectedFeedback = ref<Feedback | null>(null)
const qaForm = reactive<QAForm>({ question: '', answer: '' })

const { openConfirmDialog } = useConfirmDialog()

const feedbacks = ref<Feedback[]>([
  {
    id: 'FB001',
    status: 'positive',
    message: 'The agent was very helpful in resolving my issue quickly!',
    qaStatus: 'pending',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    context: {
      chatId: 'CH123',
      userId: 'U456',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
      pageUrl: '/pricing',
      agentVersion: '1.2.0',
    },
  },
  {
    id: 'FB002',
    status: 'negative',
    message:
      'Could not find information about pricing plans. The agent kept repeating the same irrelevant information about features. It was frustrating.',
    qaStatus: 'created',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 days ago
    context: {
      chatId: 'CH124',
      userId: 'U457',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
      pageUrl: '/support',
      agentVersion: '1.2.0',
    },
  },
  {
    id: 'FB003',
    status: 'positive',
    message: 'Excellent service!',
    qaStatus: 'pending',
    createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 mins ago
    context: {
      chatId: 'CH125',
      userId: 'U458',
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      pageUrl: '/dashboard',
      agentVersion: '1.2.0',
    },
  },
])

// Computed
const filteredFeedback = computed(() => {
  return feedbacks.value.filter((feedback: Feedback) => {
    const searchLower = searchQuery.value.toLowerCase()
    const matchesSearch =
      searchLower === '' ||
      feedback.message.toLowerCase().includes(searchLower) ||
      feedback.id.toLowerCase().includes(searchLower)

    const matchesStatus = statusFilter.value === 'all' || feedback.status === statusFilter.value
    const matchesQa = qaFilter.value === 'all' || feedback.qaStatus === qaFilter.value

    return matchesSearch && matchesStatus && matchesQa
  })
})

// Methods
const formatDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  }).format(dateObj)
}

const handleViewDetails = (feedback: Feedback) => {
  selectedFeedback.value = feedback
  isDetailsOpen.value = true
}

const handleCreateQA = (feedback: Feedback) => {
  selectedFeedback.value = feedback
  // Pre-fill form, maybe use message as question?
  qaForm.question = feedback.message // Example pre-fill
  qaForm.answer = '' // Clear previous answer
  isQaFormOpen.value = true
}

const handleSaveQA = (formData: QAForm) => {
  // TODO: Implement actual API call to save the Q&A
  // Update the corresponding feedback qaStatus
  if (selectedFeedback.value) {
    const index = feedbacks.value.findIndex((f: Feedback) => f.id === selectedFeedback.value?.id)

    if (index !== -1) {
      feedbacks.value[index] = { ...feedbacks.value[index], qaStatus: 'created' }
    }
  }

  // Optionally: Show a success toast
  isQaFormOpen.value = false // Close the form dialog
}

const handleDeleteFeedback = async (feedback: Feedback) => {
  const confirmed = await openConfirmDialog({
    title: 'Delete Feedback?',
    description: `Are you sure you want to delete the feedback: "${feedback.message.substring(0, 50)}..."? This action cannot be undone.`,
    confirmText: 'Delete',
    cancelText: 'Cancel',
  })

  if (confirmed) {
    // TODO: Implement actual API call to delete feedback
    feedbacks.value = feedbacks.value.filter((f: Feedback) => f.id !== feedback.id)
    // Optionally: Show a success toast
  }
}
</script>

<style scoped>
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
