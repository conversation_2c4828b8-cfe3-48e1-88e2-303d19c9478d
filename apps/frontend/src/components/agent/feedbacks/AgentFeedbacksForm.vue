<template>
  <BaseDialog :open="open" @update:open="(value: boolean) => emit('update:open', value)">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Create Q&A Entry</DialogTitle>
        <DialogDescription>
          Create a new Question & Answer pair based on the feedback received.
        </DialogDescription>
      </DialogHeader>
      <form @submit.prevent="saveQA" class="space-y-4">
        <div class="space-y-2">
          <Label for="question">Question</Label>
          <Input id="question" v-model="localForm.question" required />
        </div>
        <div class="space-y-2">
          <Label for="answer">Answer</Label>
          <Textarea id="answer" v-model="localForm.answer" :rows="4" required />
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" @click="emit('update:open', false)">
            Cancel
          </Button>
          <Button type="submit" :disabled="!isFormValid">Create Q&A</Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </BaseDialog>
</template>

<script setup lang="ts">
import { type QAForm } from './types'

const props = defineProps<{
  open: boolean
  initialFormData: QAForm
}>()
const emit = defineEmits(['update:open', 'save'])

const { initialFormData } = toRefs(props)

const localForm = reactive({ ...initialFormData.value })

watch(
  initialFormData,
  (newValue) => {
    Object.assign(localForm, newValue)
  },
  { deep: true }
)

const isFormValid = computed(() => {
  return localForm.question.trim() !== '' && localForm.answer.trim() !== ''
})

const saveQA = () => {
  if (isFormValid.value) {
    emit('save', { ...localForm })
    emit('update:open', false)
  }
}
</script>
