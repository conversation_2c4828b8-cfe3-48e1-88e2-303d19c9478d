<template>
  <div class="space-y-6">
    <div class="space-y-6">
      <div class="flex items-center space-x-2">
        <QrCode class="w-5 h-5 text-primary" />
        <h3 class="text-lg font-medium text-foreground">Scan QR Code</h3>
      </div>
      <p class="text-sm text-foreground ml-7">
        Share it with your users to start chatting instantly.
      </p>
    </div>

    <BaseCard title="Your Agent QR Code" description="Scan with a mobile device to start chatting">
      <div class="flex justify-end mb-4">
        <Button variant="ghost" size="sm" class="h-8 px-2" @click="downloadQRCode">
          <Download class="w-4 h-4 mr-2" />
          <span class="text-sm">Download</span>
        </Button>
      </div>

      <div class="flex items-center justify-center p-8 bg-white rounded-lg">
        <QRCodeVue3
          ref="qrCode"
          :value="directLink"
          :width="300"
          :height="300"
          :margin="2"
          :dots-options="{
            type: 'dots',
            color: '#000000',
          }"
          :background-options="{
            color: '#FFFFFF',
          }"
          :corners-square-options="{
            type: 'dot',
            color: '#000000',
          }"
          :corners-dot-options="{
            type: 'dot',
            color: '#000000',
          }"
          render-as="svg"
        />
      </div>
      <canvas ref="downloadCanvas" class="hidden" width="900" height="900" />
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import QRCodeVue3 from 'qrcode-vue3'

const props = defineProps<{
  agentUid: string
}>()
const qrCode = ref<InstanceType<typeof QRCodeVue3> | null>(null)
const downloadCanvas = ref<HTMLCanvasElement | null>(null)

const directLink = computed(() => {
  return `https://app.insertchat.com/embed/${props.agentUid}`
})

const downloadQRCode = async () => {
  if (!qrCode.value || !downloadCanvas.value) {
    console.error('Required elements not found')
    return
  }

  try {
    // Wait for the next tick to ensure the component is rendered
    await nextTick()

    const canvas = downloadCanvas.value
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.error('Could not get canvas context')
      return
    }

    // Get the QR code SVG element
    const qrElement = qrCode.value.$el
    const svgElement = qrElement.getElementsByTagName('svg')[0]

    if (!svgElement) {
      console.error('SVG element not found')
      return
    }

    // Create a data URL from the SVG
    const svgData = new XMLSerializer().serializeToString(svgElement)
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' })
    const url = URL.createObjectURL(svgBlob)

    // Create image from SVG
    const img = new Image()
    img.onload = () => {
      // Fill white background
      ctx.fillStyle = 'white'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Draw QR code
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

      // Create download link
      const link = document.createElement('a')
      link.download = `insertchat-qr-code-${props.agentUid}.png`
      link.href = canvas.toDataURL('image/png', 1.0)

      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Cleanup
      URL.revokeObjectURL(url)
    }

    img.onerror = (error) => {
      console.error('Error loading image:', error)
    }

    img.src = url
  } catch (error) {
    console.error('Error downloading QR code:', error)
  }
}
</script>
