<template>
  <div class="space-y-6">
    <div class="space-y-6">
      <div class="flex items-center space-x-2">
        <FileText class="w-5 h-5 text-primary" />
        <h3 class="text-lg font-medium text-foreground">Install in 2 Steps</h3>
      </div>

      <div class="ml-1 space-y-6">
        <div class="flex items-start space-x-3">
          <Badge variant="outline" class="px-2 py-1 text-sm"> 1 </Badge>
          <div class="space-y-1">
            <p class="text-sm font-medium text-foreground">
              Add the code below to your website's <code>&lt;head&gt;</code> tag
            </p>
          </div>
        </div>
        <div class="flex items-start space-x-3">
          <Badge variant="outline" class="px-2 py-1 text-sm"> 2 </Badge>
          <div class="space-y-1">
            <p class="text-sm font-medium text-foreground">
              Add this <code>&lt;div&gt;</code> where you want the chat to appear
            </p>
            <div class="relative p-3 font-mono text-sm rounded-md bg-muted/50 dark:bg-muted/10">
              <div class="max-w-full overflow-x-auto">
                <pre
                  class="break-words whitespace-pre-wrap"
                ><code class="text-foreground">{{ containerTemplate }}</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <BaseCard title="Installation Code" description="Copy and paste this code to your website">
      <div class="flex justify-end mb-4">
        <Button variant="ghost" size="sm" class="h-8 px-2" @click="copyCode">
          <Copy class="w-4 h-4 mr-2" />
          <span class="text-sm">Copy</span>
        </Button>
      </div>

      <div class="relative p-6 font-mono text-sm rounded-md bg-muted/50 dark:bg-muted/10">
        <div class="max-w-full overflow-x-auto">
          <pre
            class="break-words whitespace-pre-wrap"
          ><code class="text-foreground">{{ scriptTemplate }}</code></pre>
        </div>
      </div>
    </BaseCard>

    <AgentInstallPlatformGuides />
    <AgentInstallConfigVariables />
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()

const containerTemplate = computed(() => {
  return '<div id="my-ai-chatbot"></div>'
})

const scriptTemplate = computed(() => {
  return `<script type="text/javascript" src="https://app.insertchat.com/widgets/chatbot.js" async><\/script>
<script>
ICG_BOT_ID = '${props.agentUid}';
ICG_BOT_TYPE = 'window';
ICG_BOT_HEIGHT = 750;
ICG_BOT_BG_COLOR = '#fff';
ICG_BOT_AUTOFOCUS = false;
ICG_BOT_OVERRIDE_OPENER = '';
ICG_USER_ID = '';
ICG_USER_EMAIL = '';
ICG_USER_FIRSTNAME = '';
ICG_USER_LASTNAME = '';
ICG_USER_TAGS = [];
ICG_USER_METADATA = {};
<\/script>`
})

const copyCode = () => {
  navigator.clipboard.writeText(scriptTemplate.value)
}
</script>
