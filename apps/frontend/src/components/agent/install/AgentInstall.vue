<template>
  <BaseDialog
    :open="true"
    title="Install"
    description="Choose your preferred installation method below. We recommend the Bubble option for most websites."
    icon="Bot"
  >
    <BaseTabs
      default-value="bubble"
      :tabs="[
        {
          value: 'bubble',
          label: 'Floating Bubble',
          icon: 'MessageSquare',
        },
        {
          value: 'window',
          label: 'In-Page',
          icon: 'LayoutTemplate',
        },
        {
          value: 'html',
          label: 'Iframe Tag',
          icon: 'Code2',
        },
        {
          value: 'url',
          label: 'Direct URL',
          icon: 'Link',
        },
        {
          value: 'qr',
          label: 'QR Code',
          icon: 'QrCode',
        },
      ]"
    >
      <TabsContent value="bubble">
        <AgentDialogInstallBubble :agentUid="agentUid" />
      </TabsContent>
      <TabsContent value="window">
        <AgentDialogInstallWindow :agentUid="agentUid" />
      </TabsContent>
      <TabsContent value="html">
        <AgentDialogInstallHtml :agentUid="agentUid" />
      </TabsContent>
      <TabsContent value="url">
        <AgentDialogInstallUrl :agentUid="agentUid" />
      </TabsContent>
      <TabsContent value="qr">
        <AgentDialogInstallQrCode :agentUid="agentUid" />
      </TabsContent>
    </BaseTabs>

    <template #footer>
      <Button variant="outline" @click="handleClose">Close</Button>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
const AgentDialogInstallBubble = defineAsyncComponent(() => import('./AgentInstallBubble.vue'))
const AgentDialogInstallWindow = defineAsyncComponent(() => import('./AgentInstallWindow.vue'))
const AgentDialogInstallHtml = defineAsyncComponent(() => import('./AgentInstallHtml.vue'))
const AgentDialogInstallUrl = defineAsyncComponent(() => import('./AgentInstallUrl.vue'))
const AgentDialogInstallQrCode = defineAsyncComponent(() => import('./AgentInstallQrCode.vue'))

defineProps<{
  title?: string
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
@media (max-width: 640px) {
  :deep(.tabs-list) {
    overflow-x: auto;
    flex-wrap: nowrap;
    padding-bottom: 0.5rem;
  }

  :deep(.tabs-trigger) {
    flex-shrink: 0;
    white-space: nowrap;
  }
}
</style>
