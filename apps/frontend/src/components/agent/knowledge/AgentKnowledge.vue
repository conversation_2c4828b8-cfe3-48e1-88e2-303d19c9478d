<template>
  <BaseDialog
    :open="true"
    title="Knowledge Base"
    description="Manage your agent's knowledge sources."
    icon="Book"
  >
    <div
      v-if="!selectedTrainingType"
      class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 md:gap-6"
    >
      <Card
        v-for="type in trainingTypes"
        :key="type.id"
        class="cursor-pointer hover:bg-muted"
        @click="selectTrainingType(type)"
      >
        <CardHeader>
          <CardTitle class="flex items-center space-x-2">
            <component :is="type.icon" class="w-5 h-5" />
            <span>{{ type.name }}</span>
          </CardTitle>
          <CardDescription>{{ type.description }}</CardDescription>
        </CardHeader>
        <CardContent>
          <p class="text-sm text-muted-foreground">{{ type.itemsCount || 0 }} items</p>
        </CardContent>
      </Card>
    </div>

    <div v-else>
      <div class="flex flex-col -mx-6 space-y-6">
        <div class="px-6 pb-4 border-b">
          <div class="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              class="gap-2 -ml-2"
              @click="selectedTrainingType = null"
            >
              <ChevronLeft class="w-4 h-4" />
              Back
            </Button>
            <Separator orientation="vertical" class="h-4" />
            <div class="flex items-center space-x-2">
              <component :is="selectedTrainingType.icon" class="w-4 h-4 text-muted-foreground" />
              <h2 class="text-lg font-semibold">{{ selectedTrainingType.name }}</h2>
            </div>
          </div>
          <p class="mt-1 text-sm text-muted-foreground">{{ selectedTrainingType.description }}</p>
        </div>

        <div class="px-6">
          <component
            :is="selectedTrainingType.component"
            :agentUid="agentUid"
            @update="handleTrainingUpdate"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <Button variant="outline" @click="handleClose">Close</Button>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
const agentDialogKnowledgeDocuments = defineAsyncComponent(
  () => import('./agent-knowledge-documents.vue')
)
const agentDialogKnowledgeUrls = defineAsyncComponent(() => import('./agent-knowledge-urls.vue'))
const agentDialogKnowledgeQa = defineAsyncComponent(() => import('./agent-knowledge-qa.vue'))
const agentDialogKnowledgeStorage = defineAsyncComponent(
  () => import('./agent-knowledge-storage.vue')
)
const agentDialogKnowledgeTexts = defineAsyncComponent(() => import('./agent-knowledge-texts.vue'))
const agentDialogKnowledgeCatalogs = defineAsyncComponent(
  () => import('./agent-knowledge-catalogs.vue')
)
const agentDialogKnowledgeYoutube = defineAsyncComponent(
  () => import('./agent-knowledge-youtube.vue')
)

defineProps<{
  title?: string
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

interface TrainingType {
  id: string
  name: string
  description: string
  icon: any
  component: any
  itemsCount?: number
}

const trainingTypes: TrainingType[] = [
  {
    id: 'documents',
    name: 'Documents',
    description: 'Upload PDF, DOCX, and TXT files',
    icon: 'FileText',
    component: agentDialogKnowledgeDocuments,
  },
  {
    id: 'urls',
    name: 'URLs',
    description: 'Add and manage web URLs',
    icon: 'Link',
    component: agentDialogKnowledgeUrls,
  },
  {
    id: 'qa',
    name: 'Q&A',
    description: 'Create question and answer pairs',
    icon: 'MessageSquare',
    component: agentDialogKnowledgeQa,
  },
  {
    id: 'storage',
    name: 'Cloud Storage',
    description: 'Connect S3 or R2 storage',
    icon: 'Cloud',
    component: agentDialogKnowledgeStorage,
  },
  {
    id: 'text',
    name: 'Raw Text',
    description: 'Add raw text content',
    icon: 'TextIcon',
    component: agentDialogKnowledgeTexts,
  },
  {
    id: 'catalogs',
    name: 'Catalogs',
    description: 'Manage products and services',
    icon: 'ShoppingBag',
    component: agentDialogKnowledgeCatalogs,
  },
  {
    id: 'youtube',
    name: 'YouTube',
    description: 'Add YouTube video content',
    icon: 'Youtube',
    component: agentDialogKnowledgeYoutube,
  },
]

const selectedTrainingType = ref<TrainingType | null>(null)

const selectTrainingType = (type: TrainingType) => {
  selectedTrainingType.value = type
}

const handleTrainingUpdate = async (data: any) => {
  // Here we'll handle the SSE updates from the backend
  // This will update the trainingTypes itemsCount and status
}

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
@media (max-width: 640px) {
  :deep(.card-header) {
    padding: 1rem;
  }

  :deep(.card-content) {
    padding: 0 1rem 1rem 1rem;
  }
}
</style>
