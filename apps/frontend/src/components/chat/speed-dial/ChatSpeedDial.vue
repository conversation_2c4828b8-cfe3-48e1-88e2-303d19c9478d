<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" size="icon" class="rounded-full">
        <BaseIcon name="Settings2" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" class="w-56">
      <DropdownMenuItem @click="openDialog('install')">
        <BaseIcon name="Code2" class="mr-2" />
        <span>Install</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="openDialog('design')">
        <BaseIcon name="Palette" class="mr-2" />
        <span>Design</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="openDialog('settings')">
        <BaseIcon name="Settings2" class="mr-2" />
        <span>Settings</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="openDialog('knowledge')">
        <BaseIcon name="Book" class="mr-2" />
        <span>Knowledge</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="openDialog('tools')">
        <BaseIcon name="Wrench" class="mr-2" />
        <span>Tools</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="openDialog('inbox')">
        <BaseIcon name="Inbox" class="mr-2" />
        <span>Inbox</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="openDialog('integration')">
        <BaseIcon name="Plug" class="mr-2" />
        <span>Integrations</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'

const dialogStore = useDialogStore()

// Helper function to open dialogs with unique IDs
const openDialog = (type: string) => {
  dialogStore.openDialog(`${type}-main`, type as any)
}
</script>
