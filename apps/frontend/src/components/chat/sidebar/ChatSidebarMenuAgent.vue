<script setup lang="ts">
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'
import { ICONS } from '@/constants'

const { isMobile } = useSidebar()
const dialogStore = useDialogStore()

// Helper function to open dialogs using the new key system
const openDialog = (key: string) => {
  // Map old string keys to new DIALOG_KEYS
  const keyMap: Record<string, any> = {
    install: DIALOG_KEYS.AGENT_INSTALL,
    design: DIALOG_KEYS.AGENT_DESIGN,
    settings: DIALOG_KEYS.AGENT_SETTINGS,
    knowledge: DIALOG_KEYS.AGENT_KNOWLEDGE,
    tools: DIALOG_KEYS.AGENT_TOOLS,
    inbox: DIALOG_KEYS.AGENT_INBOX,
    integration: DIALOG_KEYS.AGENT_INTEGRATION,
  }

  const dialogKey = keyMap[key]
  if (dialogKey) {
    dialogStore.openDialog(dialogKey)
  }
}
</script>

<template>
  <SidebarMenuItem>
    <!-- Agent Settings -->
    <SidebarMenuButton as-child size="sm">
      <a href="#">
        <BaseIcon :name="ICONS.AGENTS" />
        <span>Agent Settings</span>
      </a>
    </SidebarMenuButton>

    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <SidebarMenuAction show-on-hover>
          <BaseIcon :name="ICONS.SETTINGS" />
          <span class="sr-only">Settings</span>
        </SidebarMenuAction>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        class="rounded-lg w-60"
        :side="isMobile ? 'bottom' : 'right'"
        align="end"
      >
        <DropdownMenuItem @click="openDialog('install')">
          <BaseIcon name="Code2" />
          <span>Install</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('design')">
          <BaseIcon name="Palette" />
          <span>Design</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('settings')">
          <BaseIcon name="Settings2" />
          <span>Settings</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('knowledge')">
          <BaseIcon name="Book" />
          <span>Knowledge</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('tools')">
          <BaseIcon name="Wrench" />
          <span>Tools</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('inbox')">
          <BaseIcon name="Inbox" />
          <span>Inbox</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('integration')">
          <BaseIcon name="Plug" />
          <span>Integrations</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </SidebarMenuItem>
</template>
