<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'
import { ICONS } from '@/constants'

const { isMobile } = useSidebar()
const dialogStore = useDialogStore()

// Helper function to open dialogs with unique IDs
const openDialog = (type: string) => {
  dialogStore.openDialog(`${type}-main`, type as any)
}
</script>

<template>
  <SidebarMenuItem>
    <!-- Agent Settings -->
    <SidebarMenuButton as-child size="sm">
      <a href="#">
        <BaseIcon :name="ICONS.AGENTS" />
        <span>Agent Settings</span>
      </a>
    </SidebarMenuButton>

    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <SidebarMenuAction show-on-hover>
          <BaseIcon :name="ICONS.SETTINGS" />
          <span class="sr-only">Settings</span>
        </SidebarMenuAction>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        class="rounded-lg w-60"
        :side="isMobile ? 'bottom' : 'right'"
        align="end"
      >
        <DropdownMenuItem @click="openDialog('install')">
          <BaseIcon name="Code2" />
          <span>Install</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('design')">
          <BaseIcon name="Palette" />
          <span>Design</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('settings')">
          <BaseIcon name="Settings2" />
          <span>Settings</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('knowledge')">
          <BaseIcon name="Book" />
          <span>Knowledge</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('tools')">
          <BaseIcon name="Wrench" />
          <span>Tools</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('inbox')">
          <BaseIcon name="Inbox" />
          <span>Inbox</span>
        </DropdownMenuItem>
        <DropdownMenuItem @click="openDialog('integration')">
          <BaseIcon name="Plug" />
          <span>Integrations</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </SidebarMenuItem>
</template>
