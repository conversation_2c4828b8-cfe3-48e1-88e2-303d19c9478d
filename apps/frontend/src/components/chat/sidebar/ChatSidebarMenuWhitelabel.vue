<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'
import { ICONS } from '@/constants'

const { isMobile } = useSidebar()
const dialogStore = useDialogStore()

// Helper function to open dialogs with unique IDs
const openDialog = (type: string) => {
  dialogStore.openDialog(`${type}-main`, type as any)
}
</script>

<template>
  <SidebarMenuItem>
    <SidebarMenuButton as-child size="sm" @click="openDialog('whitelabel')">
      <a href="#">
        <BaseIcon :name="ICONS.WHITELABEL" />
        <span>White-Label Settings</span>
      </a>
    </SidebarMenuButton>

    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <SidebarMenuAction show-on-hover>
          <BaseIcon :name="ICONS.SETTINGS" />
          <span class="sr-only">White-Label Settings</span>
        </SidebarMenuAction>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        class="rounded-lg w-60"
        :side="isMobile ? 'bottom' : 'right'"
        align="end"
      >
        <DropdownMenuItem @click="openDialog('install')">
          <BaseIcon name="Code2" />
          <span>SMTP</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </SidebarMenuItem>
</template>
