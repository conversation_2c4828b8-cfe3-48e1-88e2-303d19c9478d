<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'
import { ICONS } from '@/constants'
import { ensureAuthMe, logout } from '@/services/auth_service'
import { useRouter } from 'vue-router'

const me = await ensureAuthMe()
const logoutMutation = logout()
const router = useRouter()
const { openDialog } = useDialogStore()

const handleLogout = async () => {
  try {
    await logoutMutation.mutateAsync()

    router.push('/auth/login')
  } catch (error) {
    toast({
      title: 'Logout Error',
      description: (error as Error)?.message || 'Failed to logout',
      variant: 'destructive',
    })
  }
}
</script>

<template>
  <SidebarMenuItem>
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <SidebarMenuButton
          size="lg"
          class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <Avatar class="w-8 h-8 rounded-lg">
            <AvatarImage :src="me.user.avatar" :alt="me.user.first_name" />
            <AvatarFallback class="rounded-lg">
              {{ me.user.initials }}
            </AvatarFallback>
          </Avatar>

          <div class="grid flex-1 text-sm leading-tight text-left">
            <span class="font-semibold truncate">{{ me.user.first_name }}</span>
            <span class="text-xs truncate">{{ me.user.email }}</span>
          </div>

          <BaseIcon name="ChevronsUpDown" class="ml-auto" />
        </SidebarMenuButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent :side-offset="4" class="rounded-lg w-60" side="bottom" align="end">
        <DropdownMenuLabel class="p-0 font-normal">
          <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <Avatar class="w-8 h-8 rounded-lg">
              <AvatarImage :src="me.user.avatar" :alt="me.user.first_name" />
              <AvatarFallback class="rounded-lg"> XYZ </AvatarFallback>
            </Avatar>

            <div class="grid flex-1 text-sm leading-tight text-left">
              <span class="font-semibold truncate">{{ me.user.first_name }}</span>
              <span class="text-xs truncate">{{ me.user.email }}</span>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuGroup>
          <DropdownMenuItem @click="openDialog('billing')">
            <BaseIcon :name="ICONS.UPGRADE" />
            Upgrade
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem @click="openDialog('account')">
            <BaseIcon :name="ICONS.ACCOUNT" />
            Account
          </DropdownMenuItem>
          <DropdownMenuItem @click="openDialog('subscriptions')">
            <BaseIcon :name="ICONS.SUBSCRIPTIONS" />
            Subscriptions
          </DropdownMenuItem>
          <DropdownMenuItem @click="openDialog('users')">
            <BaseIcon :name="ICONS.USERS" />
            Users
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem @click="openDialog('support')">
            <BaseIcon :name="ICONS.SUPPORT" />
            Support
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem @click="handleLogout">
          <BaseIcon :name="ICONS.LOGOUT" />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </SidebarMenuItem>
</template>
