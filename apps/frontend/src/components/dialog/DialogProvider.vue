<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'
import { storeToRefs } from 'pinia'

const dialogStore = useDialogStore()
const { state, dialogs_instances } = storeToRefs(dialogStore)
</script>

<template>
  <!-- Legacy single dialog for backward compatibility -->
  <Suspense>
    <template #default>
      <component :is="state.component" v-if="state.isOpen" v-bind="state.props" />
    </template>
    <template #fallback>
      <div>Loading...</div>
    </template>
  </Suspense>

  <!-- Multiple dialog instances -->
  <template v-for="[id, instance] in dialogs_instances" :key="id">
    <Suspense>
      <template #default>
        <component :is="instance.component" v-if="instance.isOpen" v-bind="instance.props" />
      </template>
      <template #fallback>
        <div>Loading...</div>
      </template>
    </Suspense>
  </template>
</template>
