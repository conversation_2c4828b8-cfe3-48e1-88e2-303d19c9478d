<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'
import { storeToRefs } from 'pinia'
import { watchEffect } from 'vue'

const dialogStore = useDialogStore()
const { dialogs_instances } = storeToRefs(dialogStore)

// Debug: Watch for changes in dialog instances
watchEffect(() => {
  console.log('DialogProvider: Dialog instances changed:', dialogs_instances.value)
})
</script>

<template>
  <!-- Dialog instances -->
  <template v-for="[id, instance] in dialogs_instances" :key="id">
    <Suspense>
      <template #default>
        <component :is="instance.component" v-if="instance.isOpen" v-bind="instance.props" />
      </template>
      <template #fallback>
        <div>Loading...</div>
      </template>
    </Suspense>
  </template>
</template>
