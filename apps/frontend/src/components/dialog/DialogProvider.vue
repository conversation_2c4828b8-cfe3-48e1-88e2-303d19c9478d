<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'
import { storeToRefs } from 'pinia'
import { watchEffect } from 'vue'

const dialogStore = useDialogStore()
const { dialogs_instances } = storeToRefs(dialogStore)

// Debug: Watch for changes in dialog instances
watchEffect(() => {
  console.log('DialogProvider: Dialog instances changed:', dialogs_instances.value)
})
</script>

<template>
  <!-- Dialog instances -->
  <div>
    <p v-if="dialogs_instances.size === 0">No dialog instances</p>
    <template v-for="[id, instance] in dialogs_instances" :key="id">
      <div>
        <p>Rendering dialog: {{ id }} ({{ instance.isOpen ? 'Open' : 'Closed' }})</p>
        <Suspense>
          <template #default>
            <component :is="instance.component" v-if="instance.isOpen" v-bind="instance.props" />
          </template>
          <template #fallback>
            <div>Loading dialog {{ id }}...</div>
          </template>
        </Suspense>
      </div>
    </template>
  </div>
</template>
