<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'
import { storeToRefs } from 'pinia'

const dialogStore = useDialogStore()
const { state } = storeToRefs(dialogStore)
</script>

<template>
  <Suspense>
    <template #default>
      <component :is="state.component" v-if="state.isOpen" v-bind="state.props" />
    </template>
    <template #fallback>
      <div>Loading...</div>
    </template>
  </Suspense>
</template>
