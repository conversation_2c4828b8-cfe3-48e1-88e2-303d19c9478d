<template>
  <BaseDialog
    :open="true"
    icon="BadgeCheck"
    title="Account Settings"
    description="Manage your profile, email, and password."
  >
    <BaseTabs
      default-value="profile"
      :tabs="[
        { value: 'profile', label: 'Profile', icon: 'User' },
        { value: 'email', label: 'Email', icon: 'Mail' },
        { value: 'password', label: 'Password', icon: 'KeyRound' },
      ]"
    >
      <TabsContent value="profile">
        <AccountProfile />
      </TabsContent>
      <TabsContent value="email">
        <AccountEmail />
      </TabsContent>
      <TabsContent value="password">
        <AccountPassword />
      </TabsContent>
    </BaseTabs>
  </BaseDialog>
</template>

<script setup lang="ts"></script>
