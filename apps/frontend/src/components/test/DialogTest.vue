<template>
  <div class="p-8 space-y-4">
    <h1 class="text-2xl font-bold">Dialog System Test</h1>
    
    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Legacy Dialog Store (Single Dialog)</h2>
      <div class="space-x-2">
        <Button @click="openLegacyDialog('users')">Open Users (Legacy)</Button>
        <Button @click="openLegacyDialog('account')">Open Account (Legacy)</Button>
        <Button @click="closeLegacyDialog">Close Legacy Dialog</Button>
      </div>
    </div>

    <div class="space-y-2">
      <h2 class="text-lg font-semibold">New Multi-Dialog System</h2>
      <div class="space-x-2">
        <Button @click="openMultiDialog('users-1', 'users')">Open Users Dialog 1</Button>
        <Button @click="openMultiDialog('users-2', 'users')">Open Users Dialog 2</Button>
        <Button @click="openMultiDialog('account-1', 'account')">Open Account Dialog 1</Button>
        <Button @click="closeMultiDialog('users-1')">Close Users 1</Button>
        <Button @click="closeMultiDialog('users-2')">Close Users 2</Button>
        <Button @click="closeMultiDialog('account-1')">Close Account 1</Button>
        <Button @click="closeAllDialogs" variant="destructive">Close All Dialogs</Button>
      </div>
    </div>

    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Local Dialog Management</h2>
      <div class="space-x-2">
        <Button @click="localDialog1.toggle()">
          Toggle Local Dialog 1 ({{ localDialog1.isOpen.value ? 'Open' : 'Closed' }})
        </Button>
        <Button @click="localDialog2.toggle()">
          Toggle Local Dialog 2 ({{ localDialog2.isOpen.value ? 'Open' : 'Closed' }})
        </Button>
      </div>
    </div>

    <div class="space-y-2">
      <h2 class="text-lg font-semibold">Dialog Status</h2>
      <div class="text-sm space-y-1">
        <p>Legacy Dialog Open: {{ dialogStore.state.isOpen }}</p>
        <p>Multi-Dialog Instances: {{ dialogStore.dialogs_instances.size }}</p>
        <p>Local Dialog 1: {{ localDialog1.isOpen.value ? 'Open' : 'Closed' }}</p>
        <p>Local Dialog 2: {{ localDialog2.isOpen.value ? 'Open' : 'Closed' }}</p>
      </div>
    </div>

    <!-- Local Dialogs -->
    <BaseDialog
      v-model:open="localDialog1.isOpen.value"
      icon="TestTube"
      title="Local Dialog 1"
      description="This is a local dialog managed with useDialog composable"
    >
      <div class="p-4">
        <p>This dialog is managed locally using the useDialog composable.</p>
        <p>It can be opened and closed independently of other dialogs.</p>
        <Button @click="localDialog1.close()" class="mt-4">Close This Dialog</Button>
      </div>
    </BaseDialog>

    <BaseDialog
      v-model:open="localDialog2.isOpen.value"
      icon="TestTube2"
      title="Local Dialog 2"
      description="Another local dialog instance"
    >
      <div class="p-4">
        <p>This is a second local dialog instance.</p>
        <p>Multiple local dialogs can be open at the same time.</p>
        <Button @click="localDialog2.close()" class="mt-4">Close This Dialog</Button>
      </div>
    </BaseDialog>
  </div>
</template>

<script setup lang="ts">
import { useDialogStore, type DialogType } from '@/stores/dialog'
import { useDialog } from '@/composables/use-dialog'
import { storeToRefs } from 'pinia'

// Dialog store for legacy and multi-dialog functionality
const dialogStore = useDialogStore()

// Local dialog instances
const localDialog1 = useDialog({ id: 'test-local-1' })
const localDialog2 = useDialog({ id: 'test-local-2' })

// Legacy dialog functions
function openLegacyDialog(type: DialogType) {
  dialogStore.openDialog(type)
}

function closeLegacyDialog() {
  dialogStore.close()
}

// Multi-dialog functions
function openMultiDialog(id: string, type: DialogType) {
  dialogStore.openDialogInstance(id, type)
}

function closeMultiDialog(id: string) {
  dialogStore.closeDialogInstance(id)
}

function closeAllDialogs() {
  dialogStore.closeAllDialogs()
  localDialog1.close()
  localDialog2.close()
}
</script>
