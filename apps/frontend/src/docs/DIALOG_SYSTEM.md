# Type-Safe Centralized Dialog System

## Overview

Our dialog system provides **type-safe, centralized dialog management** with full TypeScript intellisense and validation. Each dialog has a unique key and typed props interface.

## Quick Start

```typescript
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'

const dialogStore = useDialogStore()

// Open any dialog with type-safe props
dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, {
  mode: 'create',        // TypeScript enforces this is required
  defaultRole: 'client'  // TypeScript provides intellisense for valid roles
})
```

## Dialog Categories

### 🤖 Agent Dialogs
- `DIALOG_KEYS.AGENT_INSTALL` - Agent installation wizard
- `DIALOG_KEYS.AGENT_DESIGN` - Agent appearance and branding
- `DIALOG_KEYS.AGENT_SETTINGS` - Agent configuration
- `DIALOG_KEYS.AGENT_KNOWLEDGE` - Knowledge base management
- `DIALOG_KEYS.AGENT_TOOLS` - Tool configuration
- `DIALOG_KEYS.AGENT_INBOX` - Conversation management
- `DIALOG_KEYS.AGENT_INTEGRATION` - Third-party integrations
- `DIALOG_KEYS.AGENT_FEEDBACKS` - Feedback overview
- `DIALOG_KEYS.AGENT_FEEDBACKS_FORM` - Create/edit feedback
- `DIALOG_KEYS.AGENT_LEADS_FORM` - Lead management form

### 📱 App Dialogs
- `DIALOG_KEYS.ACCOUNT` - User account settings
- `DIALOG_KEYS.USERS` - User management overview
- `DIALOG_KEYS.USERS_FORM` - Create/edit user form
- `DIALOG_KEYS.SUBSCRIPTIONS` - Subscription management
- `DIALOG_KEYS.SUPPORT` - Support ticket system

### 🎨 Whitelabel Dialogs
- `DIALOG_KEYS.WHITELABEL` - Whitelabel settings overview
- `DIALOG_KEYS.WHITELABEL_COPYRIGHTS_FORM` - Copyright management
- `DIALOG_KEYS.WHITELABEL_DOMAINS_FORM` - Domain configuration
- `DIALOG_KEYS.WHITELABEL_KEYS_FORM` - API key management
- `DIALOG_KEYS.WHITELABEL_SMTPS_FORM` - SMTP configuration

## Type-Safe Examples

### Required Props
```typescript
// ✅ Agent Settings - requires agentId
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentId: 'agent-123',           // Required
  section: 'integrations'         // Optional: 'general' | 'advanced' | 'integrations'
})

// ✅ Users Form - requires mode
dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, {
  mode: 'create',                 // Required: 'create' | 'edit'
  defaultRole: 'client'           // Optional: 'owner' | 'manager' | 'client'
})
```

### Complex Form Props
```typescript
// ✅ Agent Feedbacks Form - complex props with validation
dialogStore.openDialog(DIALOG_KEYS.AGENT_FEEDBACKS_FORM, {
  agentId: 'agent-123',           // Required
  mode: 'reply',                  // Required: 'create' | 'edit' | 'reply'
  feedbackId: 'feedback-456',     // Optional
  parentFeedbackId: 'parent-789'  // Optional (for replies)
})

// ✅ Whitelabel Domain Form
dialogStore.openDialog(DIALOG_KEYS.WHITELABEL_DOMAINS_FORM, {
  mode: 'edit',                   // Required: 'create' | 'edit'
  domainId: 'domain-123',         // Optional
  parentDomainId: 'parent-456'    // Optional
})
```

### Optional Props Only
```typescript
// ✅ Account Dialog - all props optional
dialogStore.openDialog(DIALOG_KEYS.ACCOUNT, {
  section: 'security'             // Optional: 'profile' | 'security' | 'preferences'
})

// ✅ Support Dialog
dialogStore.openDialog(DIALOG_KEYS.SUPPORT, {
  ticketId: 'ticket-789',         // Optional
  category: 'technical'           // Optional: 'technical' | 'billing' | 'general'
})
```

## TypeScript Benefits

### 1. **Intellisense & Autocomplete**
- Full autocomplete for dialog keys
- Prop suggestions based on dialog type
- Enum value suggestions for string unions

### 2. **Compile-Time Validation**
- Required props are enforced
- Invalid prop names are caught
- Type mismatches are prevented

### 3. **Refactoring Safety**
- Rename props across all usages
- Find all references to specific dialogs
- Safe prop interface changes

## Advanced Usage

### Multiple Instances
```typescript
// Form dialogs allow multiple instances
const instance1 = dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, { mode: 'create' })
const instance2 = dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, { mode: 'edit', userId: '123' })

// Main dialogs auto-close previous instances
dialogStore.openDialog(DIALOG_KEYS.USERS) // Only one can be open
```

### Dialog Management
```typescript
// Close specific dialog
dialogStore.closeDialog(instanceId)

// Close all instances of a dialog type
dialogStore.closeDialogByKey(DIALOG_KEYS.USERS_FORM)

// Check if dialog type is open
const isOpen = dialogStore.isDialogTypeOpen(DIALOG_KEYS.USERS)

// Get all instances of a dialog type
const instances = dialogStore.getDialogInstances(DIALOG_KEYS.USERS_FORM)
```

## Adding New Dialogs

### 1. Add Dialog Key
```typescript
// In stores/dialog.ts
export const DIALOG_KEYS = {
  // ... existing keys
  NEW_FEATURE: 'app.new-feature',
} as const
```

### 2. Define Props Interface
```typescript
export interface NewFeatureProps extends BaseDialogProps {
  featureId: string           // Required
  mode: 'view' | 'edit'      // Required
  section?: string           // Optional
}
```

### 3. Add to Props Map
```typescript
export interface DialogPropsMap {
  // ... existing mappings
  'app.new-feature': NewFeatureProps
}
```

### 4. Add Configuration
```typescript
export const dialogConfigs: Record<DialogKey, DialogConfig> = {
  // ... existing configs
  [DIALOG_KEYS.NEW_FEATURE]: {
    key: DIALOG_KEYS.NEW_FEATURE,
    component: defineAsyncComponent(() => import('@/components/NewFeature.vue')),
    allowMultiple: false,
  },
}
```

### 5. Use with Full Type Safety
```typescript
dialogStore.openDialog(DIALOG_KEYS.NEW_FEATURE, {
  featureId: 'feature-123',  // TypeScript enforces required props
  mode: 'edit',              // TypeScript provides intellisense
  section: 'advanced'        // Optional props work seamlessly
})
```

## Best Practices

1. **Always use DIALOG_KEYS** - Never hardcode dialog key strings
2. **Define clear prop interfaces** - Make required vs optional props obvious
3. **Use descriptive prop names** - Follow existing naming conventions
4. **Group related dialogs** - Use consistent key prefixes (agent., app., etc.)
5. **Test with TypeScript** - Ensure all prop combinations compile correctly
