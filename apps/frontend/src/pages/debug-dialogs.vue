<template>
  <LayoutDefault>
    <div class="p-8 space-y-4">
      <h1 class="text-2xl font-bold">Dialog Debug</h1>
      
      <div class="space-y-2">
        <h2 class="text-lg font-semibold">Test Dialog Opening</h2>
        <div class="space-x-2">
          <Button @click="testOpenUsers">Test Open Users Dialog</Button>
          <Button @click="testOpenAccount">Test Open Account Dialog</Button>
          <Button @click="closeAll">Close All</Button>
        </div>
      </div>

      <div class="space-y-2">
        <h2 class="text-lg font-semibold">Dialog Store State</h2>
        <div class="text-sm space-y-1">
          <p>Dialog Instances Count: {{ dialogStore.dialogs_instances.size }}</p>
          <div v-for="[id, instance] in dialogStore.dialogs_instances" :key="id" class="ml-4">
            <p>- {{ id }}: {{ instance.isOpen ? 'Open' : 'Closed' }} ({{ instance.component?.name || 'Unknown' }})</p>
          </div>
        </div>
      </div>
    </div>
  </LayoutDefault>
</template>

<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog'
import { storeToRefs } from 'pinia'

const dialogStore = useDialogStore()

function testOpenUsers() {
  console.log('Opening users dialog...')
  dialogStore.openDialog('users-test', 'users')
  console.log('Dialog store state:', dialogStore.dialogs_instances)
}

function testOpenAccount() {
  console.log('Opening account dialog...')
  dialogStore.openDialog('account-test', 'account')
  console.log('Dialog store state:', dialogStore.dialogs_instances)
}

function closeAll() {
  console.log('Closing all dialogs...')
  dialogStore.closeAllDialogs()
  console.log('Dialog store state:', dialogStore.dialogs_instances)
}
</script>
