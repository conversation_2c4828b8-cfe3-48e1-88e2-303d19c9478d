<template>
  <LayoutDefault>
    <div class="p-8 space-y-6">
      <h1 class="text-3xl font-bold">Debug Close Dialog</h1>
      
      <div class="space-y-4">
        <Button @click="openUserForm">Open User Form</Button>
        <Button @click="testCloseFunction" variant="outline">Test Close Function</Button>
      </div>

      <div class="space-y-2">
        <h2 class="text-lg font-semibold">Debug Info</h2>
        <div class="bg-gray-100 p-4 rounded text-sm">
          <div v-for="[id, instance] in dialogStore.dialogs_instances" :key="id">
            <p><strong>Instance ID:</strong> {{ id }}</p>
            <p><strong>Key:</strong> {{ instance.key }}</p>
            <p><strong>Is Open:</strong> {{ instance.isOpen }}</p>
            <p><strong>Props:</strong></p>
            <pre class="ml-4 text-xs">{{ JSON.stringify(instance.props, null, 2) }}</pre>
            <p><strong>Has onCloseDialog:</strong> {{ !!instance.props?.onCloseDialog }}</p>
            <Button size="sm" @click="testInstanceClose(instance)" class="mt-2">
              Test Instance Close
            </Button>
            <hr class="my-2">
          </div>
        </div>
      </div>
    </div>
  </LayoutDefault>
</template>

<script setup lang="ts">
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'

const dialogStore = useDialogStore()

function openUserForm() {
  console.log('Opening user form...')
  const instanceId = dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, {
    uid: 'test-user-123'
  })
  console.log('Created instance:', instanceId)
}

function testCloseFunction() {
  console.log('Testing close function...')
  
  // Get the first dialog instance
  const firstInstance = Array.from(dialogStore.dialogs_instances.values())[0]
  if (firstInstance) {
    console.log('Found instance:', firstInstance.id)
    console.log('Instance props:', firstInstance.props)
    console.log('Has onCloseDialog:', !!firstInstance.props?.onCloseDialog)
    
    if (firstInstance.props?.onCloseDialog) {
      console.log('Calling onCloseDialog...')
      firstInstance.props.onCloseDialog()
    } else {
      console.log('No onCloseDialog function found!')
    }
  } else {
    console.log('No dialog instances found')
  }
}

function testInstanceClose(instance: any) {
  console.log('Testing instance close for:', instance.id)
  console.log('Instance props:', instance.props)
  
  if (instance.props?.onCloseDialog) {
    console.log('Calling onCloseDialog...')
    instance.props.onCloseDialog()
  } else {
    console.log('No onCloseDialog function!')
  }
}
</script>
