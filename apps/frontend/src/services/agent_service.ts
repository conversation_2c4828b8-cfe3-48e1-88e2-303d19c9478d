import { queryClient } from '@/plugins/tanstack-query'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useApi } from '@/plugins/api'
import { type Ref, computed } from 'vue'
import type { AgentCreatePayload, AgentUpdatePayload, AgentUpdateOnboardingPayload } from '@/types'

export const queryAgents = (filters?: Ref<AgentFiltersPayload>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['agents', filters?.value],
    queryFn: () => api.agents.$get(filters?.value ? { query: filters.value } : {}).unwrap(),
  })
}

export const queryAgentById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['agents', uid.value],
    queryFn: () => api.agents({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

/**
 * Query to get agent factory settings.
 */
export const queryAgentFactorySettings = () => {
  const api = useApi()
  return useQuery({
    queryKey: ['agents', 'factory-settings'],
    queryFn: () => api.agents['factory-settings'].$get().unwrap(),
  })
}

/**
 * Query to get agent factory tools.
 */
export const queryAgentFactoryTools = () => {
  const api = useApi()
  return useQuery({
    queryKey: ['agents', 'factory-tools'],
    queryFn: () => api.agents['factory-tools'].$get().unwrap(),
  })
}

// --- Mutations ---

/**
 * Mutation to create a new agent.
 * Payload type is inferred from the API definition.
 */
export const mutateAgentCreate = () => {
  const api = useApi()
  return useMutation({
    mutationFn: (
      payload: AgentStorePayload // Use imported interface
    ) => api.agents.$post({ body: payload } as any).unwrap(), // Wrap in body and cast for now, review Tuyau client expectations
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

/**
 * Mutation to update an existing agent.
 */
export const mutateAgentUpdate = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: (
      payload: AgentUpdatePayload // Use imported interface
    ) =>
      api
        .agents({ uid: uid.value! })
        .$put({ body: payload } as any)
        .unwrap(), // Wrap in body and cast for now
    onSuccess: (_data, _variables, _context) => {
      queryClient.invalidateQueries({ queryKey: ['agents', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

/**
 * Mutation to delete an agent by their UID.
 */
export const mutateAgentDelete = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: () => api.agents({ uid: uid.value! }).$delete().unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

/**
 * Mutation to duplicate an agent without training.
 * This is a GET request but treated as a mutation due to its action.
 */
export const mutateAgentDuplicate = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: () => api.agents.duplicate({ uid: uid.value! }).$get().unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

/**
 * Mutation to duplicate an agent with training.
 * This is a GET request but treated as a mutation.
 */
export const mutateAgentDuplicateWithTraining = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: () => api.agents['duplicate-with-training']({ uid: uid.value! }).$get().unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

/**
 * Mutation to update an agent's image.
 * Expects FormData as payload.
 */
export const mutateAgentUpdateImage = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: (payload: FormData) =>
      api.agents['update-image']({ uid: uid.value! })
        .$put(payload as any)
        .unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents', uid.value] })
    },
  })
}

/**
 * Mutation to update an agent's onboarding information.
 */
export const mutateAgentUpdateOnboarding = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: (
      payload: AgentUpdateOnboardingPayload // Use imported interface
    ) =>
      api.agents['update-onboarding']({ uid: uid.value! })
        .$put({ body: payload } as any)
        .unwrap(), // Wrap in body and cast for now
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}
