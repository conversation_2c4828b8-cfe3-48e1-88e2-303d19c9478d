import { ref, markRaw, type Component } from 'vue'
import { defineStore } from 'pinia'
import { defineAsyncComponent } from 'vue'

export interface DialogState {
  isOpen: boolean
  component: Component | null
  props?: Record<string, any>
}

export type DialogType =
  | 'install'
  | 'design'
  | 'settings'
  | 'knowledge'
  | 'tools'
  | 'inbox'
  | 'integration'
  | 'whitelabel'
  | 'account'
  | 'users'
  | 'billing'
  | 'account'
  | 'support'

export interface DialogProps {
  [key: string]: any
}

export const dialogs = {
  install: defineAsyncComponent(() => import('@/components/agent/install/AgentInstall.vue')),
  design: defineAsyncComponent(() => import('@/components/agent/design/AgentDesign.vue')),
  settings: defineAsyncComponent(() => import('@/components/agent/settings/AgentSettings.vue')),
  knowledge: defineAsyncComponent(() => import('@/components/agent/knowledge/AgentKnowledge.vue')),
  tools: defineAsyncComponent(() => import('@/components/agent/tools/AgentTools.vue')),
  inbox: defineAsyncComponent(() => import('@/components/agent/inbox/AgentInbox.vue')),
  integration: defineAsyncComponent(
    () => import('@/components/agent/integration/AgentIntegration.vue')
  ),
  whitelabel: defineAsyncComponent(() => import('@/components/whitelabel/Whitelabel.vue')),
  account: defineAsyncComponent(() => import('@/components/account/Account.vue')),
  users: defineAsyncComponent(() => import('@/components/users/Users.vue')),
  subscriptions: defineAsyncComponent(() => import('@/components/subscriptions/Subscriptions.vue')),
  support: defineAsyncComponent(() => import('@/components/support/Support.vue')),
} as const

export const useDialogStore = defineStore('dialog', () => {
  const state = ref<DialogState>({
    isOpen: false,
    component: null,
    props: {},
  })

  function openDialog(type: DialogType, props?: DialogProps) {
    const dialogComponent = dialogs[type]

    if (!dialogComponent) {
      console.error(`Dialog type "${type}" not found`)
      return
    }

    state.value.component = markRaw(dialogComponent)
    state.value.props = props || {}
    state.value.isOpen = true
  }

  function close() {
    state.value.isOpen = false

    setTimeout(() => {
      state.value.component = null
      state.value.props = {}
    }, 100)
  }

  return {
    state,
    openDialog,
    close,
  }
})
