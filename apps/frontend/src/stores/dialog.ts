import { ref, markRaw, type Component } from 'vue'
import { defineStore } from 'pinia'
import { defineAsyncComponent } from 'vue'

export interface DialogState {
  isOpen: boolean
  component: Component | null
  props?: Record<string, any>
}

export interface DialogInstance {
  id: string
  isOpen: boolean
  component: Component | null
  props?: Record<string, any>
}

export type DialogType =
  | 'install'
  | 'design'
  | 'settings'
  | 'knowledge'
  | 'tools'
  | 'inbox'
  | 'integration'
  | 'whitelabel'
  | 'account'
  | 'users'
  | 'subscriptions'
  | 'support'

export interface DialogProps {
  [key: string]: any
}

export const dialogs = {
  install: defineAsyncComponent(() => import('@/components/agent/install/AgentInstall.vue')),
  design: defineAsyncComponent(() => import('@/components/agent/design/AgentDesign.vue')),
  settings: defineAsyncComponent(() => import('@/components/agent/settings/AgentSettings.vue')),
  knowledge: defineAsyncComponent(() => import('@/components/agent/knowledge/AgentKnowledge.vue')),
  tools: defineAsyncComponent(() => import('@/components/agent/tools/AgentTools.vue')),
  inbox: defineAsyncComponent(() => import('@/components/agent/inbox/AgentInbox.vue')),
  integration: defineAsyncComponent(
    () => import('@/components/agent/integration/AgentIntegration.vue')
  ),
  whitelabel: defineAsyncComponent(() => import('@/components/whitelabel/Whitelabel.vue')),
  account: defineAsyncComponent(() => import('@/components/account/Account.vue')),
  users: defineAsyncComponent(() => import('@/components/users/Users.vue')),
  subscriptions: defineAsyncComponent(() => import('@/components/subscriptions/Subscriptions.vue')),
  support: defineAsyncComponent(() => import('@/components/support/Support.vue')),
} as const

export const useDialogStore = defineStore('dialog', () => {
  // Legacy single dialog state for backward compatibility
  const state = ref<DialogState>({
    isOpen: false,
    component: null,
    props: {},
  })

  // New multiple dialogs state
  const dialogs_instances = ref<Map<string, DialogInstance>>(new Map())

  // Legacy functions for backward compatibility
  function openDialog(type: DialogType, props?: DialogProps) {
    const dialogComponent = dialogs[type]

    if (!dialogComponent) {
      console.error(`Dialog type "${type}" not found`)
      return
    }

    state.value.component = markRaw(dialogComponent)
    state.value.props = props || {}
    state.value.isOpen = true
  }

  function close() {
    state.value.isOpen = false

    setTimeout(() => {
      state.value.component = null
      state.value.props = {}
    }, 100)
  }

  // New functions for multiple dialogs
  function openDialogInstance(id: string, type: DialogType, props?: DialogProps) {
    const dialogComponent = dialogs[type]

    if (!dialogComponent) {
      console.error(`Dialog type "${type}" not found`)
      return
    }

    dialogs_instances.value.set(id, {
      id,
      isOpen: true,
      component: markRaw(dialogComponent),
      props: props || {},
    })
  }

  function closeDialogInstance(id: string) {
    const instance = dialogs_instances.value.get(id)
    if (instance) {
      instance.isOpen = false

      setTimeout(() => {
        dialogs_instances.value.delete(id)
      }, 100)
    }
  }

  function getDialogInstance(id: string) {
    return dialogs_instances.value.get(id)
  }

  function isDialogOpen(id: string) {
    const instance = dialogs_instances.value.get(id)
    return instance?.isOpen || false
  }

  function closeAllDialogs() {
    // Close legacy dialog
    close()

    // Close all dialog instances
    dialogs_instances.value.forEach((instance) => {
      instance.isOpen = false
    })

    setTimeout(() => {
      dialogs_instances.value.clear()
    }, 100)
  }

  return {
    // Legacy API
    state,
    openDialog,
    close,

    // New multi-dialog API
    dialogs_instances,
    openDialogInstance,
    closeDialogInstance,
    getDialogInstance,
    isDialogOpen,
    closeAllDialogs,
  }
})
