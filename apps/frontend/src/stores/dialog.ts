import { ref, markRaw, type Component } from 'vue'
import { defineStore } from 'pinia'
import { defineAsyncComponent } from 'vue'

export interface DialogInstance {
  id: string
  isOpen: boolean
  component: Component | null
  props?: Record<string, any>
}

export type DialogType =
  | 'install'
  | 'design'
  | 'settings'
  | 'knowledge'
  | 'tools'
  | 'inbox'
  | 'integration'
  | 'whitelabel'
  | 'account'
  | 'users'
  | 'subscriptions'
  | 'support'

export interface DialogProps {
  [key: string]: any
}

export const dialogs = {
  install: defineAsyncComponent(() => import('@/components/agent/install/AgentInstall.vue')),
  design: defineAsyncComponent(() => import('@/components/agent/design/AgentDesign.vue')),
  settings: defineAsyncComponent(() => import('@/components/agent/settings/AgentSettings.vue')),
  knowledge: defineAsyncComponent(() => import('@/components/agent/knowledge/AgentKnowledge.vue')),
  tools: defineAsyncComponent(() => import('@/components/agent/tools/AgentTools.vue')),
  inbox: defineAsyncComponent(() => import('@/components/agent/inbox/AgentInbox.vue')),
  integration: defineAsyncComponent(
    () => import('@/components/agent/integration/AgentIntegration.vue')
  ),
  whitelabel: defineAsyncComponent(() => import('@/components/whitelabel/Whitelabel.vue')),
  account: defineAsyncComponent(() => import('@/components/account/Account.vue')),
  users: defineAsyncComponent(() => import('@/components/users/Users.vue')),
  subscriptions: defineAsyncComponent(() => import('@/components/subscriptions/Subscriptions.vue')),
  support: defineAsyncComponent(() => import('@/components/support/Support.vue')),
} as const

export const useDialogStore = defineStore('dialog', () => {
  // Multiple dialogs state
  const dialogs_instances = ref<Map<string, DialogInstance>>(new Map())

  // Open dialog instance
  function openDialog(id: string, type: DialogType, props?: DialogProps) {
    const dialogComponent = dialogs[type]

    if (!dialogComponent) {
      console.error(`Dialog type "${type}" not found`)
      return
    }

    // Add a close handler to the props so the dialog can close itself
    const dialogProps = {
      ...props,
      onCloseDialog: () => closeDialog(id),
    }

    dialogs_instances.value.set(id, {
      id,
      isOpen: true,
      component: markRaw(dialogComponent),
      props: dialogProps,
    })
  }

  // Close dialog instance
  function closeDialog(id: string) {
    const instance = dialogs_instances.value.get(id)
    if (instance) {
      instance.isOpen = false

      setTimeout(() => {
        dialogs_instances.value.delete(id)
      }, 100)
    }
  }

  // Get dialog instance
  function getDialog(id: string) {
    return dialogs_instances.value.get(id)
  }

  // Check if dialog is open
  function isDialogOpen(id: string) {
    const instance = dialogs_instances.value.get(id)
    return instance?.isOpen || false
  }

  // Close all dialogs
  function closeAllDialogs() {
    dialogs_instances.value.forEach((instance) => {
      instance.isOpen = false
    })

    setTimeout(() => {
      dialogs_instances.value.clear()
    }, 100)
  }

  return {
    dialogs_instances,
    openDialog,
    closeDialog,
    getDialog,
    isDialogOpen,
    closeAllDialogs,
  }
})
