import { ref, markRaw, type Component } from 'vue'
import { defineStore } from 'pinia'
import { defineAsyncComponent } from 'vue'

export interface DialogInstance {
  id: string
  key: string
  isOpen: boolean
  component: Component | null
  props?: Record<string, any>
}

export interface DialogConfig {
  key: string
  component: Component
  allowMultiple?: boolean
}

export interface DialogProps {
  [key: string]: any
}

// Centralized dialog registry with unique keys
export const DIALOGS = {
  // Agent dialogs
  AGENT_INSTALL: 'agent.install',
  AGENT_DESIGN: 'agent.design',
  AGENT_SETTINGS: 'agent.settings',
  AGENT_KNOWLEDGE: 'agent.knowledge',
  AGENT_TOOLS: 'agent.tools',
  AGENT_INBOX: 'agent.inbox',
  AGENT_INTEGRATION: 'agent.integration',
  AGENT_FEEDBACKS: 'agent.feedbacks',
  AGENT_FEEDBACKS_FORM: 'agent.feedbacks.form',
  AGENT_LEADS_FORM: 'agent.leads.form',

  // App dialogs
  ACCOUNT: 'app.account',
  USERS: 'app.users',
  USERS_FORM: 'app.users.form',
  SUBSCRIPTIONS: 'app.subscriptions',
  SUPPORT: 'app.support',

  // Whitelabel dialogs
  WHITELABEL: 'app.whitelabel',
  WHITELABEL_COPYRIGHTS_FORM: 'app.whitelabel.copyrights.form',
  WHITELABEL_DOMAINS_FORM: 'app.whitelabel.domains.form',
  WHITELABEL_KEYS_FORM: 'app.whitelabel.keys.form',
  WHITELABEL_SMTPS_FORM: 'app.whitelabel.smtps.form',
} as const

export type DialogKey = (typeof DIALOGS)[keyof typeof DIALOGS]

// Dialog configurations
export const dialogConfigs: Record<DialogKey, DialogConfig> = {
  [DIALOGS.AGENT_INSTALL]: {
    key: DIALOGS.AGENT_INSTALL,
    component: defineAsyncComponent(() => import('@/components/agent/install/AgentInstall.vue')),
    allowMultiple: false,
  },
  [DIALOGS.AGENT_DESIGN]: {
    key: DIALOGS.AGENT_DESIGN,
    component: defineAsyncComponent(() => import('@/components/agent/design/AgentDesign.vue')),
    allowMultiple: false,
  },
  [DIALOGS.AGENT_SETTINGS]: {
    key: DIALOGS.AGENT_SETTINGS,
    component: defineAsyncComponent(() => import('@/components/agent/settings/AgentSettings.vue')),
    allowMultiple: false,
  },
  [DIALOGS.AGENT_KNOWLEDGE]: {
    key: DIALOGS.AGENT_KNOWLEDGE,
    component: defineAsyncComponent(
      () => import('@/components/agent/knowledge/AgentKnowledge.vue')
    ),
    allowMultiple: false,
  },
  [DIALOGS.AGENT_TOOLS]: {
    key: DIALOGS.AGENT_TOOLS,
    component: defineAsyncComponent(() => import('@/components/agent/tools/AgentTools.vue')),
    allowMultiple: false,
  },
  [DIALOGS.AGENT_INBOX]: {
    key: DIALOGS.AGENT_INBOX,
    component: defineAsyncComponent(() => import('@/components/agent/inbox/AgentInbox.vue')),
    allowMultiple: false,
  },
  [DIALOGS.AGENT_INTEGRATION]: {
    key: DIALOGS.AGENT_INTEGRATION,
    component: defineAsyncComponent(
      () => import('@/components/agent/integration/AgentIntegration.vue')
    ),
    allowMultiple: false,
  },
  [DIALOGS.AGENT_FEEDBACKS]: {
    key: DIALOGS.AGENT_FEEDBACKS,
    component: defineAsyncComponent(
      () => import('@/components/agent/feedbacks/AgentFeedbacks.vue')
    ),
    allowMultiple: false,
  },
  [DIALOGS.AGENT_FEEDBACKS_FORM]: {
    key: DIALOGS.AGENT_FEEDBACKS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/agent/feedbacks/AgentFeedbacksForm.vue')
    ),
    allowMultiple: true,
  },
  [DIALOGS.AGENT_LEADS_FORM]: {
    key: DIALOGS.AGENT_LEADS_FORM,
    component: defineAsyncComponent(() => import('@/components/agent/leads/AgentLeadsForm.vue')),
    allowMultiple: true,
  },

  // App dialogs
  [DIALOGS.ACCOUNT]: {
    key: DIALOGS.ACCOUNT,
    component: defineAsyncComponent(() => import('@/components/account/Account.vue')),
    allowMultiple: false,
  },
  [DIALOGS.USERS]: {
    key: DIALOGS.USERS,
    component: defineAsyncComponent(() => import('@/components/users/Users.vue')),
    allowMultiple: false,
  },
  [DIALOGS.USERS_FORM]: {
    key: DIALOGS.USERS_FORM,
    component: defineAsyncComponent(() => import('@/components/users/UsersForm.vue')),
    allowMultiple: true,
  },
  [DIALOGS.SUBSCRIPTIONS]: {
    key: DIALOGS.SUBSCRIPTIONS,
    component: defineAsyncComponent(() => import('@/components/subscriptions/Subscriptions.vue')),
    allowMultiple: false,
  },
  [DIALOGS.SUPPORT]: {
    key: DIALOGS.SUPPORT,
    component: defineAsyncComponent(() => import('@/components/support/Support.vue')),
    allowMultiple: false,
  },

  // Whitelabel dialogs
  [DIALOGS.WHITELABEL]: {
    key: DIALOGS.WHITELABEL,
    component: defineAsyncComponent(() => import('@/components/whitelabel/Whitelabel.vue')),
    allowMultiple: false,
  },
  [DIALOGS.WHITELABEL_COPYRIGHTS_FORM]: {
    key: DIALOGS.WHITELABEL_COPYRIGHTS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/whitelabel/copyrights/WhitelabelCopyrightsForm.vue')
    ),
    allowMultiple: true,
  },
  [DIALOGS.WHITELABEL_DOMAINS_FORM]: {
    key: DIALOGS.WHITELABEL_DOMAINS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/whitelabel/domains/WhitelabelDomainsForm.vue')
    ),
    allowMultiple: true,
  },
  [DIALOGS.WHITELABEL_KEYS_FORM]: {
    key: DIALOGS.WHITELABEL_KEYS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/whitelabel/keys/WhitelabelKeysForm.vue')
    ),
    allowMultiple: true,
  },
  [DIALOGS.WHITELABEL_SMTPS_FORM]: {
    key: DIALOGS.WHITELABEL_SMTPS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/whitelabel/smtps/WhitelabelSmtpsForm.vue')
    ),
    allowMultiple: true,
  },
}

export const useDialogStore = defineStore('dialog', () => {
  // Dialog instances state
  const dialogs_instances = ref<Map<string, DialogInstance>>(new Map())

  // Generate unique instance ID
  function generateInstanceId(key: DialogKey): string {
    return `${key}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
  }

  // Open dialog by key
  function openDialog(key: DialogKey, props?: DialogProps): string {
    const config = dialogConfigs[key]

    if (!config) {
      console.error(`Dialog with key "${key}" not found`)
      return ''
    }

    // Check if dialog allows multiple instances
    if (!config.allowMultiple) {
      // Close existing instance of this dialog type
      const existingInstance = Array.from(dialogs_instances.value.values()).find(
        (instance) => instance.key === key
      )

      if (existingInstance) {
        closeDialog(existingInstance.id)
      }
    }

    // Generate unique instance ID
    const instanceId = generateInstanceId(key)

    // Add close handler to props
    const dialogProps = {
      ...props,
      onCloseDialog: () => closeDialog(instanceId),
    }

    // Create dialog instance
    const instance: DialogInstance = {
      id: instanceId,
      key,
      isOpen: true,
      component: markRaw(config.component),
      props: dialogProps,
    }

    dialogs_instances.value.set(instanceId, instance)
    return instanceId
  }

  // Close dialog by instance ID
  function closeDialog(instanceId: string) {
    const instance = dialogs_instances.value.get(instanceId)
    if (instance) {
      instance.isOpen = false

      setTimeout(() => {
        dialogs_instances.value.delete(instanceId)
      }, 100)
    }
  }

  // Close dialog by key (closes all instances of this dialog type)
  function closeDialogByKey(key: DialogKey) {
    const instancesToClose = Array.from(dialogs_instances.value.values()).filter(
      (instance) => instance.key === key
    )

    instancesToClose.forEach((instance) => {
      closeDialog(instance.id)
    })
  }

  // Get dialog instance by ID
  function getDialog(instanceId: string) {
    return dialogs_instances.value.get(instanceId)
  }

  // Check if dialog is open by instance ID
  function isDialogOpen(instanceId: string) {
    const instance = dialogs_instances.value.get(instanceId)
    return instance?.isOpen || false
  }

  // Check if dialog type is open by key
  function isDialogTypeOpen(key: DialogKey) {
    return Array.from(dialogs_instances.value.values()).some(
      (instance) => instance.key === key && instance.isOpen
    )
  }

  // Get all instances of a dialog type
  function getDialogInstances(key: DialogKey) {
    return Array.from(dialogs_instances.value.values()).filter((instance) => instance.key === key)
  }

  // Close all dialogs
  function closeAllDialogs() {
    dialogs_instances.value.forEach((instance) => {
      instance.isOpen = false
    })

    setTimeout(() => {
      dialogs_instances.value.clear()
    }, 100)
  }

  return {
    // State
    dialogs_instances,

    // Core functions
    openDialog,
    closeDialog,
    closeDialogByKey,

    // Query functions
    getDialog,
    isDialogOpen,
    isDialogTypeOpen,
    getDialogInstances,

    // Utility functions
    closeAllDialogs,
  }
})
