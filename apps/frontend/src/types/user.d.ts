import { InferRequestType, InferResponseType } from '@tuyau/client'
import { useApi } from '@/plugins/api'

const api = useApi()

export type Users = InferResponseType<typeof api.users.$get>
export type User = Users['data'][number]
export type UserCreatePayload = InferRequestType<typeof api.users.$post>
export type UserUpdatePayload = InferRequestType<
  ReturnType<ReturnType<typeof useApi>['users']>['$put']
>
