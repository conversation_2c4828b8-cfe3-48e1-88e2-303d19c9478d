---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Frontend Dialogs System

**Rule Name**: `frontend_dialogs`

# Type-Safe Centralized Dialog System with Automatic Prop Extraction

## Overview

Our dialog system provides **type-safe, centralized dialog management** with **automatic prop extraction** from Vue components. No manual prop definitions needed!

## ✨ Key Features

- **🎯 Automatic Prop Extraction** - Props are extracted directly from Vue components
- **🔒 Full Type Safety** - TypeScript intellisense and validation
- **🚀 Zero Duplication** - No need to define props twice
- **📝 Self-Documenting** - Component props serve as the source of truth

## Quick Start

```typescript
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'

const dialogStore = useDialogStore()

// Props are automatically extracted from the component!
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentUid: 'agent-123', // Required - extracted from component
  section: 'behavior', // Optional - with full intellisense
})
```

## How Automatic Prop Extraction Works

### 1. Define Props in Your Component

```vue
<!-- AgentSettings.vue -->
<script setup lang="ts">
defineProps<{
  agentUid: string // Required prop
  section?: 'general' | 'behavior' // Optional prop with enum
  onCloseDialog?: () => void // Auto-added by dialog store
}>()
</script>
```

### 2. Props Are Automatically Extracted

```typescript
// In stores/dialog.ts - NO MANUAL DEFINITION NEEDED!
export type AgentSettingsProps = ExtractComponentProps<typeof AgentSettingsComponent>

// TypeScript automatically knows:
// - agentUid is required (string)
// - section is optional ('general' | 'behavior')
// - onCloseDialog is auto-injected by the store
```

### 3. Use with Full Type Safety

```typescript
// ✅ TypeScript enforces required props
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentUid: 'agent-123', // Required - won't compile without this
})

// ✅ TypeScript provides intellisense for optional props
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentUid: 'agent-123',
  section: 'behavior', // Intellisense shows: 'general' | 'behavior'
})
```

## Dialog Categories

### 🤖 Agent Dialogs

- `DIALOG_KEYS.AGENT_INSTALL` - Agent installation wizard
- `DIALOG_KEYS.AGENT_DESIGN` - Agent appearance and branding
- `DIALOG_KEYS.AGENT_SETTINGS` - Agent configuration
- `DIALOG_KEYS.AGENT_KNOWLEDGE` - Knowledge base management
- `DIALOG_KEYS.AGENT_TOOLS` - Tool configuration
- `DIALOG_KEYS.AGENT_INBOX` - Conversation management
- `DIALOG_KEYS.AGENT_INTEGRATION` - Third-party integrations
- `DIALOG_KEYS.AGENT_FEEDBACKS` - Feedback overview
- `DIALOG_KEYS.AGENT_FEEDBACKS_FORM` - Create/edit feedback
- `DIALOG_KEYS.AGENT_LEADS_FORM` - Lead management form

### 📱 App Dialogs

- `DIALOG_KEYS.ACCOUNT` - User account settings
- `DIALOG_KEYS.USERS` - User management overview
- `DIALOG_KEYS.USERS_FORM` - Create/edit user form
- `DIALOG_KEYS.SUBSCRIPTIONS` - Subscription management
- `DIALOG_KEYS.SUPPORT` - Support ticket system

### 🎨 Whitelabel Dialogs

- `DIALOG_KEYS.WHITELABEL` - Whitelabel settings overview
- `DIALOG_KEYS.WHITELABEL_COPYRIGHTS_FORM` - Copyright management
- `DIALOG_KEYS.WHITELABEL_DOMAINS_FORM` - Domain configuration
- `DIALOG_KEYS.WHITELABEL_KEYS_FORM` - API key management
- `DIALOG_KEYS.WHITELABEL_SMTPS_FORM` - SMTP configuration

## Type-Safe Examples

### Required Props

```typescript
// ✅ Agent Settings - requires agentId
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentId: 'agent-123', // Required
  section: 'integrations', // Optional: 'general' | 'advanced' | 'integrations'
})

// ✅ Users Form - requires mode
dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, {
  mode: 'create', // Required: 'create' | 'edit'
  defaultRole: 'client', // Optional: 'owner' | 'manager' | 'client'
})
```

### Complex Form Props

```typescript
// ✅ Agent Feedbacks Form - complex props with validation
dialogStore.openDialog(DIALOG_KEYS.AGENT_FEEDBACKS_FORM, {
  agentId: 'agent-123', // Required
  mode: 'reply', // Required: 'create' | 'edit' | 'reply'
  feedbackId: 'feedback-456', // Optional
  parentFeedbackId: 'parent-789', // Optional (for replies)
})

// ✅ Whitelabel Domain Form
dialogStore.openDialog(DIALOG_KEYS.WHITELABEL_DOMAINS_FORM, {
  mode: 'edit', // Required: 'create' | 'edit'
  domainId: 'domain-123', // Optional
  parentDomainId: 'parent-456', // Optional
})
```

### Optional Props Only

```typescript
// ✅ Account Dialog - all props optional
dialogStore.openDialog(DIALOG_KEYS.ACCOUNT, {
  section: 'security', // Optional: 'profile' | 'security' | 'preferences'
})

// ✅ Support Dialog
dialogStore.openDialog(DIALOG_KEYS.SUPPORT, {
  ticketId: 'ticket-789', // Optional
  category: 'technical', // Optional: 'technical' | 'billing' | 'general'
})
```

## Advanced Usage

### Multiple Instances

```typescript
// Form dialogs allow multiple instances
const instance1 = dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, { mode: 'create' })
const instance2 = dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, { mode: 'edit', userId: '123' })

// Main dialogs auto-close previous instances
dialogStore.openDialog(DIALOG_KEYS.USERS) // Only one can be open
```

### Dialog Management

```typescript
// Close specific dialog
dialogStore.closeDialog(instanceId)

// Close all instances of a dialog type
dialogStore.closeDialogByKey(DIALOG_KEYS.USERS_FORM)

// Check if dialog type is open
const isOpen = dialogStore.isDialogTypeOpen(DIALOG_KEYS.USERS)

// Get all instances of a dialog type
const instances = dialogStore.getDialogInstances(DIALOG_KEYS.USERS_FORM)
```

## Adding New Dialogs (Super Easy with Auto-Extraction!)

### 1. Create Your Dialog Component

```vue
<!-- NewFeature.vue -->
<template>
  <BaseDialog :title="title">
    <!-- Your dialog content -->
  </BaseDialog>
</template>

<script setup lang="ts">
// Define your props - they'll be automatically extracted!
defineProps<{
  featureId: string // Required
  mode: 'view' | 'edit' // Required with enum
  section?: string // Optional
  onCloseDialog?: () => void // Auto-injected by dialog store
}>()

const title = computed(() => `${props.mode === 'edit' ? 'Edit' : 'View'} Feature`)
</script>
```

### 2. Add to Dialog Store (5 simple steps)

```typescript
// In stores/dialog.ts

// Step 1: Add dialog key
export const DIALOG_KEYS = {
  // ... existing keys
  NEW_FEATURE: 'app.new-feature',
} as const

// Step 2: Import component for prop extraction
import type NewFeatureComponent from '@/components/NewFeature.vue'

// Step 3: Add prop type extraction
export type NewFeatureProps = ExtractComponentProps<typeof NewFeatureComponent> & BaseDialogProps

// Step 4: Add to props map
export interface DialogPropsMap {
  // ... existing mappings
  'app.new-feature': NewFeatureProps
}

// Step 5: Add configuration
export const dialogConfigs: Record<DialogKey, DialogConfig> = {
  // ... existing configs
  [DIALOG_KEYS.NEW_FEATURE]: {
    key: DIALOG_KEYS.NEW_FEATURE,
    component: defineAsyncComponent(() => import('@/components/NewFeature.vue')),
    allowMultiple: false,
  },
}
```

### 3. Use with Automatic Type Safety

```typescript
// ✅ Props automatically extracted from component!
dialogStore.openDialog(DIALOG_KEYS.NEW_FEATURE, {
  featureId: 'feature-123', // Required - TypeScript enforces
  mode: 'edit', // Required - intellisense shows 'view' | 'edit'
  section: 'advanced', // Optional - works seamlessly
})
```

## Best Practices

1. **Always use DIALOG_KEYS** - Never hardcode dialog key strings
2. **Define clear prop interfaces** - Make required vs optional props obvious
3. **Use descriptive prop names** - Follow existing naming conventions
4. **Group related dialogs** - Use consistent key prefixes (agent., app., etc.)
5. **Test with TypeScript** - Ensure all prop combinations compile correctly