---
description: 
globs: apps/frontend/**
alwaysApply: false
---
### Rule: Base Component Documentation & Usage Guide

**Rule Name**: `base_components`

This document outlines the available base components, their properties (props), and common usage patterns. Refer to this guide when implementing UI elements to ensure consistency and leverage existing functionality.

* Always prefer base components if available:
  * [BaseCard.vue](mdc:apps/frontend/src/components/base/card/BaseCard.vue) instead of [Card.vue](mdc:apps/frontend/src/shadcn/ui/card/Card.vue)
  * [BaseTabs.vue](mdc:apps/frontend/src/components/base/tabs/BaseTabs.vue) instead of [Tabs.vue](mdc:apps/frontend/src/shadcn/ui/tabs/Tabs.vue)
  * [BaseForm.vue](mdc:apps/frontend/src/components/base/form-field/BaseForm.vue) instead of `form`
  * [BaseSkeleton.vue](mdc:apps/frontend/src/components/base/skeleton/BaseSkeleton.vue) instead of [Skeleton.vue](mdc:apps/frontend/src/shadcn/ui/skeleton/Skeleton.vue)
  * [BaseButton.vue](mdc:apps/frontend/src/components/base/button/BaseButton.vue) instead of [Button.vue](mdc:apps/frontend/src/shadcn/ui/button/Button.vue)
  * [BaseDialog.vue](mdc:apps/frontend/src/components/base/dialog/BaseDialog.vue) instead of [Dialog.vue](mdc:apps/frontend/src/shadcn/ui/dialog/Dialog.vue)
  * [BaseTextarea.vue](mdc:apps/frontend/src/components/base/textarea/BaseTextarea.vue) instead of [Textarea.vue](mdc:apps/frontend/src/shadcn/ui/textarea/Textarea.vue)
  * [BaseFormField.vue](mdc:apps/frontend/src/components/base/form-field/BaseFormField.vue) instead of [FormItem.vue](mdc:apps/frontend/src/shadcn/ui/form/FormItem.vue)
  * [BaseDropzone.vue](mdc:apps/frontend/src/components/base/dropzone/BaseDropzone.vue)
  * [BaseColorPicker.vue](mdc:apps/frontend/src/components/base/color-picker/BaseColorPicker.vue)

```vue
<!-- ✅ Correct -->
<BaseButton :isSubmitting="isSubmitting" :isDisabled="false">
  Save Changes
</BaseButton>

<BaseTable 
  :heads="heads" 
  :rows="data" 
  :actions="['edit', 'delete']"
  @edit="handleEdit"
/>

<BaseDialog :icon="ICONS.SETTINGS" title="Settings" description="Configure settings">
  <slot />
</BaseDialog>

<!-- ❌ Wrong -->
<Button :disabled="isSubmitting">Save Changes</Button>
<Table><!-- manual table structure --></Table>
<Dialog><!-- manual dialog structure --></Dialog>

#### 🔹 **`BaseButton.vue`**

*   **Purpose**: A wrapper around the Shadcn `Button` component, enhanced with an `isSubmitting` state to automatically show a loading spinner and disable the button.
*   **Props**:
    *   `isSubmitting`: `Boolean` (required) - If true, disables the button and shows a `Loader2` icon.
    *   `isDisabled`: `Boolean` (required) - If true, disables the button.
*   **Slots**:
    *   Default: Content of the button (e.g., text).
*   **Attributes**: Inherits attributes to the underlying Shadcn `Button` (`v-bind="$attrs"`).
*   **Usage**: Ideal for action buttons that trigger an asynchronous operation, like form submissions.

---

#### 🔹 **`BaseCard.vue`**

*   **Purpose**: Provides a structured card layout, wrapping Shadcn `Card` elements.
*   **Props**:
    *   `title`: `String` (optional) - The title displayed in the card header.
    *   `description`: `String` (optional) - A description displayed below the title in the card header.
*   **Slots**:
    *   Default: Main content of the card, placed within `CardContent`.
    *   `footer`: Content for the `CardFooter` section.
*   **Usage**: Use to group related information or UI elements within a visually distinct bordered container.

---

#### 🔹 **`BaseColorPicker.vue`**

*   **Purpose**: A custom input component for selecting a hex color value with a visual preview.
*   **Props**:
    *   `modelValue`: `String | null` (optional) - The currently selected hex color string (e.g., `#RRGGBB`).
*   **Emits**:
    *   `update:modelValue (value: string)`: Emitted when the color value changes.
*   **Usage**: For settings or forms where a user needs to pick a specific color.

---

#### 🔹 **`BaseDialog.vue`**

*   **Purpose**: A full-screen dialog component that supports both global store management and local state control. It wraps Shadcn `Dialog` components and includes a header, scrollable content area, and a footer slot.
*   **Props**:
    *   `title`: `String` (required) - Title displayed in the dialog header.
    *   `description`: `String` (required) - Description displayed below the title.
    *   `icon`: `String` (required) - Name of the `BaseIcon` to display next to the title.
    *   `open`: `Boolean` (optional) - Controls dialog visibility locally. When provided, overrides global store behavior.
*   **Emits**:
    *   `update:open`: `(value: boolean)` - Emitted when dialog open state changes (only when `open` prop is used).
*   **Slots**:
    *   Default: Main content of the dialog.
    *   `footer`: For actions or buttons in the dialog footer.
*   **Dependencies**: `useDialogStore`, `BaseIcon`, `ScrollArea`.
*   **Usage Modes**:
    *   **Global Store Mode** (default): Control via `useDialogStore` - for main app dialogs
    *   **Local Control Mode**: Use `v-model:open` for independent form dialogs
*   **Examples**:
    ```vue
    <!-- Global store mode (main component dialogs) -->
    <BaseDialog :icon="ICONS.USERS" title="Users" description="Manage users">
      <slot />
    </BaseDialog>
    
    <!-- Local control mode (form dialogs) -->
    <BaseDialog 
      v-model:open="isFormOpen" 
      :icon="ICONS.USERS" 
      title="Add User" 
      description="Create new user"
    >
      <slot />
    </BaseDialog>
    ```

---

#### 🔹 **`BaseDropzone.vue`**

*   **Purpose**: A comprehensive file input component supporting drag & drop, multiple file uploads, previews (for images), file type/size validation, and progress display (requires manual progress updates).
*   **Props**:
    *   `accept`: `String` (optional) - Comma-separated string of accepted file types (e.g., `image/png,image/jpeg,.pdf`).
    *   `acceptText`: `String` (optional, default: 'Upload files') - Text displayed in the dropzone.
    *   `multiple`: `Boolean` (optional, default: `true`) - Allows multiple file selection.
    *   `maxSize`: `Number` (optional, default: `10485760` (10MB)) - Maximum file size in bytes.
    *   `maxFiles`: `Number` (optional) - Maximum number of files allowed if `multiple` is true.
    *   `initialFiles`: `File[]` (optional, default: `[]`) - An array of `File` objects to pre-populate the dropzone.
    *   `previewHeightClass`: `String` (optional, default: `'h-[160px]'`) - TailwindCSS class for the preview area height.
*   **Emits**:
    *   `files-change (files: File[])`: Emitted when the list of selected/staged files changes.
*   **Exposes**:
    *   `clearFiles()`: A method to programmatically clear all files from the dropzone.
*   **Usage**: For user-friendly file uploading in forms or dedicated file management interfaces.

---

#### 🔹 **`BaseForm.vue`**

*   **Purpose**: A wrapper component for HTML `<form>` elements. It manages `isLoading` and `isSubmitting` states, displaying a `BaseSkeleton` of type "form" during `isLoading`. It also disables form content (`pointer-events-none opacity-60`) when `isLoading` or `isSubmitting` is true.
*   **Props**:
    *   `isLoading`: `Boolean` (required) - If true, shows a form skeleton.
    *   `isSubmitting`: `Boolean` (required) - If true (along with `isLoading` being false), disables form content.
    *   `inputs`: `Number` (required) - Number of input rows for the skeleton.
*   **Emits**:
    *   `submit (event: Event)`: Emitted when the form is submitted (and not disabled).
*   **Slots**:
    *   Default: Contains the actual form fields and inputs.
*   **Usage**: Wrap your entire form content with `BaseForm` to handle loading skeletons and submission states consistently.

---

#### 🔹 **`BaseFormField.vue`**

*   **Purpose**: A layout component to structure individual form fields. It provides slots for a label, description, the input control itself, and displays error or helper text.
*   **Props**:
    *   `label`: `String` (optional) - Text for the form field label.
    *   `description`: `String` (optional) - Additional descriptive text below the label.
    *   `error`: `String` (optional) - Error message to display for the field.
    *   `helperText`: `String` (optional) - Additional helper text below the input area.
*   **Slots**:
    *   Default: Place the actual input component (e.g., `Input`, `Textarea`, `Select`) here.
*   **Usage**: Use to wrap each input element within a form for consistent layout of labels, descriptions, and error messages.

---

#### 🔹 **`BaseIcon.vue`**

*   **Purpose**: Dynamically renders icons from the `lucide-vue-next` library.
*   **Props**:
    *   `name`: `String` (required) - The PascalCase name of the Lucide icon (e.g., 'User', 'Settings').
    *   `size`: `Number` (optional, default: `16`) - Size of the icon in pixels.
    *   `color`: `String` (optional, default: `'currentColor'`) - Color of the icon.
    *   `strokeWidth`: `Number` (optional, default: `2`) - Stroke width of the icon.
    *   `defaultClass`: `String` (optional, default: `'w-5 h-5 shrink-0'`) - Default CSS classes applied to the icon.
*   **Usage**: For displaying vector icons throughout the application.

---

#### 🔹 **`BaseSkeleton.vue`**

*   **Purpose**: Provides various pre-defined skeleton loading placeholders for common UI patterns.
*   **Props**:
    *   `type`: `String` (required) - The type of skeleton to render (e.g., 'card', 'table', 'chat', 'form', 'app').
    *   `rows`: `Number` (required) - Context-dependent: number of rows for table/form skeletons.
    *   `headers`: `Number` (required) - Context-dependent: number of header columns for table skeleton.
    *   `cols`: `Number` (required) - Context-dependent: number of columns per row for table skeleton.
*   **Usage**: Display while data is being fetched to improve perceived performance and indicate loading activity. Match the `type` to the content being loaded.

---

#### 🔹 **`BaseTable.vue`**

*   **Purpose**: A configurable table component with support for loading states (using `BaseSkeleton`), headers, data rows, row-level actions (edit/delete), and an empty state message.
*   **Props**:
    *   `title`: `String` (optional) - Title for the table area (not directly rendered in `BaseTable` itself but can be used by parent).
    *   `description`: `String` (optional) - Description for the table area.
    *   `heads`: `Array<{ label: string, key: string }>` (required) - Defines table headers. `label` is displayed, `key` is used to access data in `rows`.
    *   `rows`: `Array<any>` (required) - Data for table rows. Each item should have an `id` or `uid` for Vue keys.
    *   `actions`: `Array<string>` (required) - Array of action names (e.g., `['edit', 'delete']`) to enable action buttons.
    *   `isLoading`: `Boolean` (required) - If true, displays a table skeleton.
    *   `emptyMessage`: `String` (required, default: 'No data available.') - Message shown when `rows` is empty and not loading.
*   **Emits**:
    *   `edit (row: any)`: Emitted when an edit action is clicked.
    *   `delete (row: any)`: Emitted when a delete action is clicked.
*   **Slots**:
    *   `actions`: Area above the table for global table actions (e.g., "Add New Item" button).
    *   `row ({ row: any })`: Allows for custom rendering of an entire table row. Overrides default cell-by-cell rendering.
    *   `column-${colIndex} ({ row: any, value: any, head: any })`: Allows for custom rendering of a specific cell in a column. `colIndex` is the 0-based index of the column.
*   **Usage**: For displaying lists of data in a structured, tabular format with common interaction patterns.

---

#### 🔹 **`BaseTabs.vue`**

*   **Purpose**: A component for creating tabbed navigation, wrapping Shadcn `Tabs`, `TabsList`, and `TabsTrigger`.
*   **Props**:
    *   `defaultValue`: `String` (required) - The `value` of the tab that should be active by default.
    *   `tabs`: `Array<{ value: string, label: string, icon: string }>` (required) - Configuration for each tab.
*   **Slots**:
    *   Default: Place `TabsContent` components here, one for each tab, matching their `value` prop to the `tab.value`.
*   **Usage**: For organizing content into different sections that can be switched by the user.

---

#### 🔹 **`BaseTextarea.vue`**

*   **Purpose**: An enhanced Shadcn `Textarea` that automatically grows in height to fit its content (up to a max height) and can display a character count.
*   **Props**:
    *   `modelValue`: `String` (required) - The v-model binding for the textarea content.
    *   `placeholder`: `String` (optional, default: `''`).
    *   `rows`: `Number` (optional, default: `1`) - Initial number of rows.
    *   `maxlength`: `Number` (optional) - If set, displays a character counter (e.g., `currentLength/maxlength`).
    *   `required`: `Boolean` (optional, default: `false`).
    *   `class`: `HTMLAttributes['class']` (optional, default: `''`) - Additional CSS classes.
*   **Emits**:
    *   `update:modelValue (value: string)`: Emitted on input.
*   **Exposes**:
    *   `resetHeight()`: Method to programmatically reset the textarea\'s height to its default.
*   **Usage**: For multi-line text inputs where dynamic height adjustment is desired.
