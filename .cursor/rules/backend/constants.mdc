---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Constants Convention

**Rule Name**: `backend_constants`

## Global Constants Organization (Real Pattern)

All global constants MUST be defined in `/app/constants.ts` exactly as implemented in the codebase:

```typescript
// Environment-based constants
export const IS_PRODUCTION = adonisApp.inProduction
```

## JSON File Loading Pattern (Real Implementation)

Load JSON configuration files with proper typing:

```typescript
// Type-safe JSON loading with validator inference
export const DEFAULT_AGENT_SETTINGS: Infer<typeof agentSettingsValidator> = JSON.parse(
  fs.readFileSync(`${APP_ROOT_FOLDER_PATH}app/resources/files/agent_settings.json`, 'utf8')
)
```

## Naming Conventions

- **Global constants**: `UPPER_SNAKE_CASE`
- **Always use** `as const` for arrays and objects
- **Always type** JSON-loaded constants with explicit types or `Infer<typeof validator>`
- **Always use** spread syntax for extending arrays (`...PARENT_ARRAY`)
- **Semantic names**: Describe the feature/action
- **No redundant suffixes**: Don't use `_ICON`, `_BTN`, etc.
- **Group logically**: Related constants together

## Import Pattern

```typescript
// Always import from the central constants file
import { DEFAULT_AGENT_SETTINGS, TRAINING_IMAGE_EXTENSIONS } from '#app/constants'
```

## Class-Specific Constants (When to Use)

Only use class-specific constants for values that are:
1. Used only within a single class
2. Not shared externally
3. Internal implementation details

```typescript
export default class EmbeddingService {
  private readonly EMBEDDING_SCHEMA_PREFIX = 'embeddings_dynamic'
  private readonly DEFAULT_MAX_RECORDS = 100
}
```

## Required Patterns Summary

1. **Always import** from `#app/constants` using named imports
2. **Always use** `UPPER_SNAKE_CASE` naming
3. **Always use** `as const` for type literals
4. **Always use** `Infer<typeof validator>` for JSON with validation
5. **Always use** semantic names that describe purpose, not implementation
6. **Always group** related constants together
7. **Always use** spread syntax for array inheritance
8. **Always type** JSON-loaded constants explicitly